# frozen_string_literal: true

require 'rails_helper'

RSpec.describe Api::V1::StudentsController, type: :controller do
  render_views
  let(:organization) { current_organization }
  let(:student) { create(:user) }
  let!(:course1) { create(:course) }
  let(:course2) { create(:course) }
  # let(:canvas_user) { create(:user) }
  let!(:enrollment1) { create(:enrollment, canvas_course_id: course1.canvas_id, base_role_type: 'StudentEnrollment', canvas_user_id: student.canvas_id) }
  let!(:enrollment2) { create(:enrollment, canvas_course_id: course2.canvas_id, base_role_type: 'StudentEnrollment', canvas_user_id: student.canvas_id) }

  let!(:module1) { create(:context_module, course: course1, canvas_context_type: 'Course') }
  let!(:module2) { create(:context_module, course: course1, canvas_context_type: 'Course') }
  let!(:module_item1) { create(:context_module_item, canvas_id: 101, context_module: module1) }
  let!(:module_item2) { create(:context_module_item, canvas_id: 102, context_module: module2, canvas_assignment_id: 1) }
  let!(:submission) { create(:submission, canvas_assignment_id: 1, canvas_user_id: student.canvas_id, workflow_state: 'unsubmitted') }
  let!(:default_course_image) { create(:global_setting) }

  let(:session) do
    PandaPal::Session.create(panda_pal_organization_id: organization.id,
                             data: {
                               canvas_user_id: student.canvas_id,
                               organization_key: organization.key
                             })
  end

  let(:default_params) { { organization_id: organization.id, session_key: session.session_key } }
  describe 'GET #courses' do
    before do
      allow(Canvas).to receive(:with_shard).and_yield
      allow(Canvas::Course).to receive(:progress_count).and_return([
                                                                     { 'id' => course1.canvas_id, 'req_count' => 2, 'req_completed_count' => 1 },
                                                                     { 'id' => course2.canvas_id, 'req_count' => 0, 'req_completed_count' => 0 }
                                                                   ])
      get :courses, params: default_params.merge(id: student.canvas_id), format: :json
    end

    it 'returns a successful response' do
      expect(response).to have_http_status(:success)
    end

    it 'returns JSON with courses' do
      json = JSON.parse(response.body)
      courses = json['courses']
      expect(courses).to be_an(Array)
      expect(courses.size).to eq(2)
      course_ids = courses.map { |c| c['canvas_course_id'] }
      expect(course_ids).to include(course1.canvas_id, course2.canvas_id)
    end

    it 'includes expected course fields' do
      json = JSON.parse(response.body)
      course = json['courses'].find { |c| c['canvas_course_id'] == course1.canvas_id }

      expect(course['id']).to eq(course1.id)
      expect(course['canvas_course_id']).to eq(course1.canvas_id)
      expect(course['name']).to eq(course1.name)
      expect(course['course_sis_id']).to eq(course1.sis_id)
      expect(course['course_code']).to eq(course1.course_code)
      expect(course['course_state']).to eq(course1.workflow_state)
      expect(course['course_image_url']).to eq(course1.image_url)
      expect(course['canvas_enrollment_id']).to eq(enrollment1.canvas_id)
      expect(course['canvas_user_id']).to eq(student.canvas_id.to_i)
      expect(course['enrollment_state']).to eq(enrollment1.workflow_state)
      expect(course['grade_level']).to be_nil
      expect(course['current_score']).to be_nil
      expect(course['current_letter_grade']).to be_nil
      expect(course['requirement_count']).to eq(2)
      expect(course['requirement_completed_count']).to eq(1)
      expect(course['organization_id']).to eq(organization.id.to_s)
      expect(course['organization_name']).to eq(organization.name)
    end
  end

  describe 'GET #course_progress' do
    before do
      organization.update(canvas_shard_id: 1)
      module1.update(completion_requirements: [{ 'id' => module_item1.canvas_id, 'type' => 'must_view' }])
      module2.update(completion_requirements: [{ 'id' => module_item2.canvas_id, 'type' => 'must_view' }])
    end

    # Helper to keep stubs concise and consistent
    # rubocop:disable Metrics/MethodLength, Metrics/ParameterLists
    def build_course_progress(score:, letter:, module2_status:, module2_item_status:, show_fireworks:, req_completed:, progress_percent:)
      {
        id: course1.id,
        canvas_id: course1.canvas_id,
        name: course1.name,
        course_code: course1.course_code,
        course_sis_id: course1.sis_id,
        current_score: score,
        current_letter_grade: letter,
        requirement_count: 2,
        requirement_completed_count: req_completed,
        past_due_requirements_count: 0,
        requirements_with_due_date_count: 0,
        course_progress_percent: progress_percent,
        expected_course_progress: 0,
        expected_course_progress_percent: 0,
        color_code: nil,
        modules: [
          {
            module_id: module1.canvas_id,
            name: module1.name,
            module_progress_status: nil,
            items: [
              {
                id: module_item1.canvas_id,
                item_type: module_item1.canvas_content_type,
                requirement_status: 'not completed'
              }
            ],
            'items_count' => 1,
            'mastered' => 0,
            'completed' => 0,
            'not mastered' => 0,
            'not completed' => 1,
            'past due' => 0
          },
          {
            module_id: module2.canvas_id,
            name: module2.name,
            module_progress_status: module2_status,
            items: [
              {
                id: module_item2.canvas_id,
                item_type: module_item2.canvas_content_type,
                requirement_status: module2_item_status,
                score: score
              }
            ],
            'items_count' => 1,
            'mastered' => (module2_item_status == 'mastered' ? 1 : 0),
            'completed' => 0,
            'not mastered' => (module2_item_status == 'not mastered' ? 1 : 0),
            'not completed' => (module2_item_status == 'not completed' ? 1 : 0),
            'past due' => 0
          }
        ],
        teacher: nil,
        show_fireworks: show_fireworks
      }
    end
    # rubocop:enable Metrics/MethodLength, Metrics/ParameterLists

    context 'when user has not completed any course requirement' do
      before do
        allow(QueryBuilder::StudentCourseProgress).to receive(:from_valid_shard).and_return(
          build_course_progress(score: nil,
                                letter: nil,
                                module2_status: nil,
                                module2_item_status: 'not completed',
                                show_fireworks: false,
                                req_completed: 0,
                                progress_percent: 0)
        )
        get :course_progress, params: default_params.merge(id: student.canvas_id, course_id: course1.canvas_id, org_shard_id: organization.canvas_shard_id)
      end

      it 'returns a successful response' do
        expect(response).to have_http_status(:success)
      end

      it 'returns JSON with course progress fields' do
        json = JSON.parse(response.body)
        expect(json).to be_a(Hash)

        expect(json['id']).to eq(course1.id)
        expect(json['canvas_id']).to eq(course1.canvas_id)
        expect(json['name']).to eq(course1.name)
        expect(json['requirement_count']).to eq(2)
        expect(json['requirement_completed_count']).to eq(0)
        expect(json['past_due_requirements_count']).to eq(0)
        expect(json['requirements_with_due_date_count']).to eq(0)
        expect(json['course_progress_percent']).to eq(0)
        expect(json['expected_course_progress']).to eq(0)
        expect(json['expected_course_progress_percent']).to eq(0)
        expect(json['modules']).to be_an(Array)
        expect(json['show_fireworks']).to eq(false)

        modules = json['modules']
        expect(modules.size).to eq(2)
        expect(modules.map { |m| m['module_id'] }).to include(module1.canvas_id, module2.canvas_id)
      end
    end

    context 'when user has completed all items of module' do
      before do
        submission.update!(workflow_state: 'submitted', score: nil)
        module2.context_module_items.each do |item|
          ContextModuleProgression.create!(
            canvas_user_id: student.canvas_id,
            canvas_course_id: course1.canvas_id,
            canvas_module_id: item.context_module.canvas_id,
            canvas_module_item_id: item.canvas_id,
            canvas_content_type: item.canvas_content_type,
            requirement_type: 'must_view',
            requirement_status: 'completed',
            module_progress_status: 'completed'
          )
        end
        allow(QueryBuilder::StudentCourseProgress).to receive(:from_valid_shard).and_return(
          build_course_progress(score: nil,
                                letter: nil,
                                module2_status: 'completed',
                                module2_item_status: 'not mastered',
                                show_fireworks: true,
                                req_completed: 1,
                                progress_percent: 50)
        )
        get :course_progress, params: default_params.merge(id: student.canvas_id, course_id: course1.canvas_id, org_shard_id: organization.canvas_shard_id)
      end

      it 'returns a successful response' do
        expect(response).to have_http_status(:success)
      end

      it 'returns JSON with course progress fields and completed requirements' do
        json = JSON.parse(response.body)
        expect(json).to be_a(Hash)

        expect(json['id']).to eq(course1.id)
        expect(json['canvas_id']).to eq(course1.canvas_id)
        expect(json['name']).to eq(course1.name)

        modules = json['modules']
        expect(modules.size).to eq(2)
        expect(modules.map { |m| m['module_id'] }).to include(module1.canvas_id, module2.canvas_id)
      end

      it 'returns JSON with requirement_count set to 2' do
        json = JSON.parse(response.body)
        expect(json['requirement_count']).to eq(2)
      end

      it 'returns JSON with requirement_completed_count set to 1' do
        json = JSON.parse(response.body)
        expect(json['requirement_completed_count']).to eq(1)
      end

      it 'returns JSON with course_progress_percent set to 50' do
        json = JSON.parse(response.body)
        expect(json['course_progress_percent']).to eq(50)
      end

      it 'returns JSON with show_fireworks set to true' do
        json = JSON.parse(response.body)
        expect(json['show_fireworks']).to eq(true)
      end

      it 'returns JSON with module items requirement_status set to completed' do
        json = JSON.parse(response.body)
        mod = json['modules'].find { |m| m['module_id'] == module2.canvas_id }
        item = mod['items'][0]
        expect(item['requirement_status']).to eq('not mastered')
      end
    end

    context 'when teacher grades to submission' do
      before do
        submission.update!(workflow_state: 'graded', score: 85)
        module2.context_module_items.each do |item|
          ContextModuleProgression.create!(
            canvas_user_id: student.canvas_id,
            canvas_course_id: course1.canvas_id,
            canvas_module_id: item.context_module.canvas_id,
            canvas_module_item_id: item.canvas_id,
            canvas_content_type: item.canvas_content_type,
            requirement_type: 'must_view',
            requirement_status: 'completed',
            module_progress_status: 'completed'
          )
        end
        allow(QueryBuilder::StudentCourseProgress).to receive(:from_valid_shard).and_return(
          build_course_progress(score: 85,
                                letter: 'B',
                                module2_status: 'completed',
                                module2_item_status: 'mastered',
                                show_fireworks: true,
                                req_completed: 1,
                                progress_percent: 50)
        )
        get :course_progress, params: default_params.merge(id: student.canvas_id, course_id: course1.canvas_id, org_shard_id: organization.canvas_shard_id)
      end

      it 'returns a successful response' do
        expect(response).to have_http_status(:success)
      end

      it 'returns JSON with show_fireworks set to true' do
        json = JSON.parse(response.body)
        expect(json['show_fireworks']).to eq(true)
      end

      it 'returns JSON with module items requirement_status set to mastered' do
        json = JSON.parse(response.body)
        mod = json['modules'].find { |m| m['module_id'] == module2.canvas_id }
        item = mod['items'][0]
        expect(item['requirement_status']).to eq('mastered')
        expect(item['score']).to eq(submission.score)
      end
    end
  end

  describe 'GET #due_assignments' do
    let(:due_date) { Time.zone.today.strftime('%Y-%m-%d') }

    before do
      allow(QueryBuilder::StudentDueAssignments).to receive(:across_shard).and_return([])
      allow(Students::CalendarEvents).to receive(:fetch_events_for_user).and_return({})
      allow(Students::CelebrationEvents).to receive(:remove_daily_event).and_return(true)
      allow(Students::CelebrationEvents).to receive(:create_daily_event).and_return(true)
      allow(Students::CelebrationEvents).to receive(:daily_event_shown?).and_return(false)
    end

    it 'fetches assignments and calendar events when no due_date' do
      get :due_assignments, params: default_params.merge(id: student.canvas_id), format: :json

      expect(response).to have_http_status(:success)
      expect(QueryBuilder::StudentDueAssignments).to have_received(:across_shard).with(instance_of(User), {})
      expect(Students::CalendarEvents).to have_received(:fetch_events_for_user).with(instance_of(User), nil, weekly: false)
    end

    it 'removes daily celebration when not all items are completed' do
      assignments = [{ 'requirement_status' => 'not completed' }]
      calendar = { due_date => [{ completed_at: nil }] }
      allow(QueryBuilder::StudentDueAssignments).to receive(:across_shard).and_return(assignments)
      allow(Students::CalendarEvents).to receive(:fetch_events_for_user).and_return(calendar)

      get :due_assignments, params: default_params.merge(id: student.canvas_id, due_date: due_date), format: :json

      expect(response).to have_http_status(:success)
      expect(Students::CelebrationEvents).to have_received(:remove_daily_event).with(instance_of(User), due_date)
      expect(Students::CelebrationEvents).not_to have_received(:create_daily_event)
    end

    it 'creates daily celebration and sets fireworks when all completed and not shown' do
      assignments = [{ 'requirement_status' => 'completed' }]
      calendar = { due_date => [{ completed_at: Time.zone.now }] }
      allow(QueryBuilder::StudentDueAssignments).to receive(:across_shard).and_return(assignments)
      allow(Students::CalendarEvents).to receive(:fetch_events_for_user).and_return(calendar)
      allow(Students::CelebrationEvents).to receive(:daily_event_shown?).and_return(false)

      get :due_assignments, params: default_params.merge(id: student.canvas_id, due_date: due_date), format: :json

      expect(response).to have_http_status(:success)
      expect(Students::CelebrationEvents).to have_received(:create_daily_event).with(instance_of(User), due_date)
    end
  end

  describe 'GET #weekly_due_assignments' do
    let(:due_date) { Time.zone.today.strftime('%Y-%m-%d') }

    before do
      allow(QueryBuilder::StudentDueAssignments).to receive(:across_shard).and_return([])
      allow(Students::CalendarEvents).to receive(:fetch_events_for_user).and_return({})
    end

    it 'fetches weekly assignments and calendar events' do
      get :weekly_due_assignments, params: default_params.merge(id: student.canvas_id, due_date: due_date), format: :json

      expect(response).to have_http_status(:success)
      expect(QueryBuilder::StudentDueAssignments).to have_received(:across_shard).with(instance_of(User), { due_date: due_date, weekly: true })
      expect(Students::CalendarEvents).to have_received(:fetch_events_for_user).with(instance_of(User), due_date, weekly: true)
    end
  end

  describe 'POST #update_student_celebration_event' do
    it 'marks daily event shown when due_date provided' do
      allow(Students::CelebrationEvents).to receive(:mark_daily_event_shown)

      post :update_student_celebration_event, params: default_params.merge(id: student.canvas_id, due_date: '2025-09-12')

      expect(response).to have_http_status(:ok)
      expect(Students::CelebrationEvents).to have_received(:mark_daily_event_shown).with(instance_of(User), '2025-09-12')
    end

    it 'marks module event shown when course_id provided' do
      allow(Students::CelebrationEvents).to receive(:mark_module_event_shown)

      post :update_student_celebration_event, params: default_params.merge(id: student.canvas_id, course_id: 123)

      expect(response).to have_http_status(:ok)
      expect(Students::CelebrationEvents).to have_received(:mark_module_event_shown).with(instance_of(User), '123')
    end

    it 'returns error when neither due_date nor course_id provided' do
      post :update_student_celebration_event, params: default_params.merge(id: student.canvas_id)

      expect(response).to have_http_status(:unprocessable_entity)
      json = JSON.parse(response.body)
      expect(json['error_message']).to match(/Due date or course ID must be provided/)
    end
  end
end
