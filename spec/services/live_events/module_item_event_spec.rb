# frozen_string_literal: true

require 'rails_helper'

RSpec.describe LiveEvents::ModuleItemEvent, type: :service do
  let(:course) { create(:course, canvas_id: 12) }
  let(:context_module) { create(:context_module, canvas_id: 34, canvas_context_id: course.canvas_id) }
  let(:context_module_item) { create(:context_module_item, canvas_id: 56, canvas_context_module_id: context_module.canvas_id) }

  let(:payload) do
    {
      body: {
        module_id: context_module.canvas_id,
        context_id: course.canvas_id,
        context_type: 'Course',
        module_item_id: context_module_item.canvas_id,
        workflow_state: 'active'
      }
    }.with_indifferent_access
  end

  let(:service) { described_class.new(payload) }

  before do
    allow(Rails.logger).to receive(:info)
  end

  describe '#process' do
    let(:context_module_double) { instance_double(ContextModule, sync_from_api: true, canvas_id: 34, canvas_context_id: 12) }
    let(:context_module_item_double) { instance_double(ContextModuleItem, sync_from_api: true, canvas_id: 56, workflow_state: 'active') }

    before do
      allow(ContextModule).to receive(:find_or_initialize_by).and_return(context_module_double)
      allow(ContextModuleItem).to receive(:find_or_initialize_by).and_return(context_module_item_double)
      allow(context_module_item_double).to receive(:workflow_state=)
    end

    it 'finds or initializes context module with correct attributes' do
      expect(ContextModule).to receive(:find_or_initialize_by).with(
        canvas_id: context_module.canvas_id,
        canvas_context_id: course.canvas_id,
        canvas_context_type: 'Course'
      )

      service.perform(payload)
    end

    it 'syncs context module from API' do
      expect(context_module_double).to receive(:sync_from_api)

      service.perform(payload)
    end

    it 'logs context module sync' do
      expect(Rails.logger).to receive(:info).with(/Context module synced/)

      service.perform(payload)
    end

    it 'finds or initializes context module item by canvas_id only' do
      expect(ContextModuleItem).to receive(:find_or_initialize_by).with(
        canvas_id: context_module_item.canvas_id,
        canvas_context_module_id: context_module.canvas_id
      )

      service.perform(payload)
    end

    it 'syncs context module item from API' do
      expect(context_module_item_double).to receive(:sync_from_api)

      service.perform(payload)
    end

    it 'logs module item sync' do
      expect(Rails.logger).to receive(:info).with(/Module item synced/)

      service.perform(payload)
    end

    context 'with cross-shard IDs' do
      let(:global_module_id) { 10_000_000_000_034 }
      let(:global_context_id) { 10_000_000_000_012 }
      let(:global_module_item_id) { 10_000_000_000_056 }

      let(:payload) do
        {
          body: {
            module_id: global_module_id,
            context_id: global_context_id,
            context_type: 'Course',
            module_item_id: global_module_item_id,
            workflow_state: 'active'
          }
        }.with_indifferent_access
      end

      it 'converts global IDs to local IDs for context module' do
        expect(ContextModule).to receive(:find_or_initialize_by).with(
          canvas_id: 34, # local ID
          canvas_context_id: 12, # local ID
          canvas_context_type: 'Course'
        )

        service.perform(payload)
      end

      it 'finds context module item using canvas_id from payload' do
        expect(ContextModuleItem).to receive(:find_or_initialize_by).with(
          canvas_id: service.send(:local_canvas_id, global_module_item_id), # NOTE: uses ID from payload,
          canvas_context_module_id: service.send(:local_canvas_id, global_module_id)
        )

        service.perform(payload)
      end
    end
  end

  describe '#local_canvas_id' do
    it 'converts global ID to local ID' do
      global_id = 10_000_000_000_123
      local_id = service.send(:local_canvas_id, global_id)

      expect(local_id).to eq(123)
    end

    it 'handles already local IDs' do
      local_id = 123
      result = service.send(:local_canvas_id, local_id)

      expect(result).to eq(123)
    end

    it 'handles string IDs' do
      global_id = '10000000000123'
      local_id = service.send(:local_canvas_id, global_id)

      expect(local_id).to eq(123)
    end
  end

  describe '#event_info' do
    it 'returns formatted event info with organization name' do
      expect(service.send(:event_info)).to eq("[Org: #{service.current_organization.name}] [ModuleItemEvent]")
    end
  end
end
