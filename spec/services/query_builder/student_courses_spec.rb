# frozen_string_literal: true

require 'rails_helper'

RSpec.describe QueryBuilder::StudentCourses do
  describe '#records' do
    let(:user) { create(:user, canvas_id: 123) }
    let(:course) { create(:course, canvas_id: 456, name: 'Course Name', course_code: 'CODE123', sis_id: 'SIS123', workflow_state: 'active') }
    let(:enrollment) { create(:enrollment, canvas_id: 789, canvas_user_id: user.canvas_id, canvas_course_id: course.canvas_id, workflow_state: 'active', base_role_type: 'StudentEnrollment') }
    let(:score) { create(:score, canvas_enrollment_id: enrollment.canvas_id, current_score: 95.0, current_letter_grade: 'A') }
    let!(:module1) { create(:context_module, course: course, canvas_context_type: 'Course') }
    let!(:module2) { create(:context_module, course: course, canvas_context_type: 'Course') }
    let!(:module_item1) { create(:context_module_item, canvas_id: 101, context_module: module1) }
    let!(:module_item2) { create(:context_module_item, canvas_id: 102, context_module: module2) }
    let(:organization_id) { 1 }
    let(:organization_name) { 'Org Name' }
    let(:organization_shard_id) { 2 }

    let(:query_builder) do
      described_class.new({
                            canvas_user_id: user.canvas_id,
                            organization_id: organization_id,
                            organization_name: organization_name,
                            organization_shard_id: organization_shard_id
                          })
    end

    before do
      allow(Canvas).to receive(:with_shard).and_yield
      allow(Canvas::Course).to receive(:progress_count).and_return([
                                                                     { 'id' => course.canvas_id, 'req_count' => 2, 'req_completed_count' => 0 }
                                                                   ])
      enrollment # ensure enrollment is created
      score # ensure score is created
      allow(query_builder).to receive(:organization_id).and_return(organization_id)
      allow(query_builder).to receive(:organization_name).and_return(organization_name)
      allow(query_builder).to receive(:organization_shard_id).and_return(organization_shard_id)
    end

    it 'returns course records with expected fields' do
      result = query_builder.records

      expect(result).to be_present
      record = result.first

      expect(record.name).to eq(course.name)
      expect(record.course_code).to eq(course.course_code)
      expect(record.course_sis_id).to eq(course.sis_id)
      expect(record.current_score).to eq(score.current_score)
      expect(record.current_letter_grade).to eq(score.current_letter_grade)
      expect(record.organization_id).to eq(organization_id.to_s)
      expect(record.organization_name).to eq(organization_name)
      expect(record.organization_shard_id).to eq(organization_shard_id)
      expect(record.requirement_count).to eq(2)
      expect(record.requirement_completed_count).to eq(0)
    end

    it 'returns course records with requirement completed count' do
      allow(Canvas::Course).to receive(:progress_count).and_return([
                                                                     { 'id' => course.canvas_id, 'req_count' => 2, 'req_completed_count' => 1 }
                                                                   ])
      result = query_builder.records
      expect(result).to be_present
      record = result.first
      expect(record.name).to eq(course.name)
      expect(record.course_code).to eq(course.course_code)
      expect(record.requirement_count).to eq(2)
      expect(record.requirement_completed_count).to eq(1)
    end
  end
end
