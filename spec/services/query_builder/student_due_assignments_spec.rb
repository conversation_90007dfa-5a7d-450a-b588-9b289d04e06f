# frozen_string_literal: true

require 'rails_helper'

RSpec.describe QueryBuilder::StudentDueAssignments do
  describe '#records' do
    let(:organization) { current_organization }
    let(:user) { create(:user, canvas_id: 123) }
    let(:course) { create(:course, canvas_id: 456, name: 'Course Name', course_code: 'CODE123', sis_id: 'SIS123', workflow_state: 'active') }
    let(:enrollment) { create(:enrollment, canvas_id: 101, canvas_user_id: user.canvas_id, canvas_course_id: course.canvas_id, workflow_state: 'active', base_role_type: 'StudentEnrollment') }
    let(:assignment1) { create(:assignment, canvas_id: 789, context: course, due_at: 1.days.from_now, title: 'Assignment 1') }
    let(:assignment2) { create(:assignment, canvas_id: 790, context: course, due_at: 2.days.from_now, title: 'Assignment 2') }
    let(:assignment3) { create(:assignment, canvas_id: 791, context: course, due_at: 3.days.from_now, title: 'Assignment 3') }
    let(:submission1) { create(:submission, canvas_assignment_id: assignment1.canvas_id, canvas_user_id: user.canvas_id, score: nil, workflow_state: 'unsubmitted') }
    let(:submission2) { create(:submission, canvas_assignment_id: assignment2.canvas_id, canvas_user_id: user.canvas_id, score: nil, workflow_state: 'unsubmitted') }
    let(:submission3) { create(:submission, canvas_assignment_id: assignment3.canvas_id, canvas_user_id: user.canvas_id, score: 95.0, workflow_state: 'unsubmitted') }

    let!(:module1) { create(:context_module, course: course, canvas_context_type: 'Course') }
    let!(:module2) { create(:context_module, course: course, canvas_context_type: 'Course') }
    let!(:module_item1) { create(:context_module_item, canvas_id: 101, context_module: module1, canvas_content_type: 'Assignment', canvas_content_id: assignment1.canvas_id, canvas_assignment_id: assignment1.canvas_id) }
    let!(:module_item2) { create(:context_module_item, canvas_id: 102, context_module: module2, canvas_content_type: 'Assignment', canvas_content_id: assignment2.canvas_id, canvas_assignment_id: assignment2.canvas_id) }
    let!(:module_item3) { create(:context_module_item, canvas_id: 103, context_module: module2, canvas_content_type: 'Assignment', canvas_content_id: assignment3.canvas_id, canvas_assignment_id: assignment3.canvas_id) }
    let!(:module_item4) { create(:context_module_item, canvas_id: 104, context_module: module2, canvas_content_type: 'DiscussionTopic', canvas_content_id: 301) }
    let!(:filters) { {} }

    let(:query_builder) do
      described_class.new({
                            canvas_user_id: user.canvas_id,
                            canvas_course_id: course.canvas_id,
                            organization: organization,
                            filters: filters
                          })
    end

    before do
      allow(Canvas).to receive(:with_shard).and_yield
      allow(Canvas::Course).to receive(:users_todo_items).and_return([{ 'context_module_item_id' => module_item4.canvas_id, 'content_type' => 'DiscussionTopic', 'content_id' => 301 }])
      allow(Canvas::Course).to receive(:users_only_past_due_todo_items).and_return([])
      enrollment # ensure enrollment is created
      # update submission1 due_at Today
      submission1.update(due_at: Time.now)
      module1.update(completion_requirements: [{ 'id' => module_item1.canvas_id, 'type' => 'must_view' }])
      module2.update(completion_requirements: [{ 'id' => module_item2.canvas_id, 'type' => 'must_view' }, { 'id' => module_item3.canvas_id, 'type' => 'must_view' }])
    end

    it 'returns all due assignments for the student with expected fields' do
      result = query_builder.records

      expect(result).to be_present
      expect(result.size).to eq(4)

      assignment_record = result.first
      expect(assignment_record['id']).to eq(module_item1.canvas_id)
      expect(assignment_record['item_type']).to eq('Assignment')
      expect(assignment_record['canvas_assignment_id']).to eq(assignment1.canvas_id)
      expect(assignment_record['assignment_title']).to eq(assignment1.title)
      expect(assignment_record['assignment_due_at']).to be_within(1.second).of(assignment1.due_at)
      expect(assignment_record['submission_due_at']).to be_within(1.second).of(submission1.due_at)
      expect(assignment_record['todo_date']).to be_nil
      expect(assignment_record['lock_at']).to be_nil
      expect(assignment_record['canvas_course_id']).to eq(course.canvas_id)
      expect(assignment_record['course_name']).to eq(course.name)
      expect(assignment_record['course_sis_id']).to eq(course.sis_id)
      expect(assignment_record['course_code']).to eq(course.course_code)
      expect(assignment_record['course_state']).to eq(course.workflow_state)
      expect(assignment_record['canvas_user_id']).to be_nil
      expect(assignment_record['submission_score']).to eq(submission1.score)
      expect(assignment_record['submission_points_possible']).to eq(submission1.points_possible)
      expect(assignment_record['submission_state']).to eq(submission1.workflow_state)
      expect(assignment_record['requirement_status']).to eq('incomplete')
      expect(assignment_record['requirement_type']).to eq('must_view')
      expect(assignment_record['canvas_page_url']).to be_nil
      expect(assignment_record['canvas_content_id']).to be_nil
      expect(assignment_record['req_status']).to eq('not started')
      expect(assignment_record['organization_id']).to eq(organization.id.to_s)
      expect(assignment_record['organization_name']).to eq(organization.name)
      expect(assignment_record['organization_base_url']).to eq(organization.base_url)
      expect(assignment_record['course_content_path']).to eq("courses/#{course.canvas_id}/modules/items/#{module_item1.canvas_id}")
      non_assignment_item = result.last
      expect(non_assignment_item['id']).to eq(module_item4.canvas_id)
      expect(non_assignment_item['item_type']).to eq('DiscussionTopic')
      expect(non_assignment_item['canvas_assignment_id']).to be_nil
    end

    it 'returns due assignments for the day' do
      filters[:due_date] = Date.today.to_s
      result = query_builder.records

      expect(result).to be_present
      expect(result.size).to eq(2)

      assignment_record = result.first
      expect(assignment_record['id']).to eq(module_item1.canvas_id)
      expect(assignment_record['item_type']).to eq('Assignment')
      expect(assignment_record['canvas_assignment_id']).to eq(assignment1.canvas_id)
      expect(assignment_record['assignment_title']).to eq(assignment1.title)
      expect(assignment_record['assignment_due_at']).to be_within(1.second).of(assignment1.due_at)
      expect(assignment_record['submission_due_at']).to be_within(1.second).of(submission1.due_at)
      expect(assignment_record['todo_date']).to be_nil
      expect(assignment_record['lock_at']).to be_nil
      expect(assignment_record['canvas_course_id']).to eq(course.canvas_id)
      expect(assignment_record['course_name']).to eq(course.name)
      expect(assignment_record['course_sis_id']).to eq(course.sis_id)
      expect(assignment_record['course_code']).to eq(course.course_code)
      expect(assignment_record['course_state']).to eq(course.workflow_state)
      expect(assignment_record['canvas_user_id']).to be_nil
      non_assignment_item = result.last
      expect(non_assignment_item['id']).to eq(module_item4.canvas_id)
      expect(non_assignment_item['item_type']).to eq('DiscussionTopic')
      expect(non_assignment_item['canvas_assignment_id']).to be_nil
    end

    it 'returns all past due assignments' do
      submission2.update(due_at: Time.now - 3.days)
      filters[:only_past_dues] = true
      result = query_builder.records

      expect(result).to be_present
      expect(result.size).to eq(1)

      assignment_record = result.first
      expect(assignment_record['id']).to eq(module_item2.canvas_id)
      expect(assignment_record['item_type']).to eq('Assignment')
      expect(assignment_record['canvas_assignment_id']).to eq(assignment2.canvas_id)
      expect(assignment_record['assignment_title']).to eq(assignment2.title)
      expect(assignment_record['assignment_due_at']).to be_within(1.second).of(assignment2.due_at)
      expect(assignment_record['submission_due_at']).to be_within(1.second).of(submission2.due_at)
      expect(assignment_record['todo_date']).to be_nil
      expect(assignment_record['lock_at']).to be_nil
      expect(assignment_record['canvas_course_id']).to eq(course.canvas_id)
      expect(assignment_record['course_name']).to eq(course.name)
      expect(assignment_record['course_sis_id']).to eq(course.sis_id)
      expect(assignment_record['course_code']).to eq(course.course_code)
      expect(assignment_record['course_state']).to eq(course.workflow_state)
      expect(assignment_record['canvas_user_id']).to be_nil
    end
  end
end
