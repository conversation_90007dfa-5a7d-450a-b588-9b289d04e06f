# frozen_string_literal: true

require 'rails_helper'

RSpec.describe QueryBuilder::StudentCourseProgress do
  describe '#filtered_out_item?' do
    let(:query_builder) { described_class.new({}) }

    context 'when filters is blank' do
      it 'does not filter non-assignment items' do
        allow(query_builder).to receive(:filters).and_return(nil)
        expect(query_builder.send(:filtered_out_item?, nil, 'completed')).to be false
        expect(query_builder.send(:filtered_out_item?, nil, 'not completed')).to be false
      end
    end

    context 'when submission is present (assignment item)' do
      it 'delegates to filtered_out?' do
        submission = instance_double(Submission)
        allow(query_builder).to receive(:filters).and_return(['not_completed'])
        expect(query_builder).to receive(:filtered_out?).with(submission).and_return(true)
        expect(query_builder.send(:filtered_out_item?, submission, 'not completed')).to be true
      end
    end

    context 'when submission is nil (non-assignment item)' do
      it 'applies filter for mastered' do
        allow(query_builder).to receive(:filters).and_return(['master'])
        expect(query_builder.send(:filtered_out_item?, nil, 'mastered')).to be true
        expect(query_builder.send(:filtered_out_item?, nil, 'not mastered')).to be false
      end

      it 'applies filter for not mastered' do
        allow(query_builder).to receive(:filters).and_return(['not_master'])
        expect(query_builder.send(:filtered_out_item?, nil, 'not mastered')).to be true
        expect(query_builder.send(:filtered_out_item?, nil, 'mastered')).to be false
      end

      it 'applies filter for past due' do
        allow(query_builder).to receive(:filters).and_return(['not_completed_past_due'])
        expect(query_builder.send(:filtered_out_item?, nil, 'past due')).to be true
      end

      it 'applies filter for not completed' do
        allow(query_builder).to receive(:filters).and_return(['not_completed'])
        expect(query_builder.send(:filtered_out_item?, nil, 'not completed')).to be true
      end

      it 'applies filter for completed when provided' do
        allow(query_builder).to receive(:filters).and_return(['completed'])
        expect(query_builder.send(:filtered_out_item?, nil, 'completed')).to be true
      end
    end
  end
  describe '#filtered_out?' do
    let(:query_builder) { described_class.new({}) }

    # Create submission doubles for each status type
    let(:mastered_submission) do
      instance_double(Submission,
                      master?: true,
                      not_master?: false,
                      not_completed?: false,
                      not_completed_past_due?: false,
                      requirement_status: 'mastered')
    end

    let(:not_mastered_submission) do
      instance_double(Submission,
                      master?: false,
                      not_master?: true,
                      not_completed?: false,
                      not_completed_past_due?: false,
                      requirement_status: 'not mastered')
    end

    let(:not_completed_submission) do
      instance_double(Submission,
                      master?: false,
                      not_master?: false,
                      not_completed?: true,
                      not_completed_past_due?: false,
                      requirement_status: 'not completed')
    end

    let(:past_due_submission) do
      instance_double(Submission,
                      master?: false,
                      not_master?: false,
                      not_completed?: false,
                      not_completed_past_due?: true,
                      requirement_status: 'past due')
    end

    context 'when filters is blank' do
      before do
        allow(query_builder).to receive(:filters).and_return(nil)
      end

      it 'returns false for any submission' do
        expect(query_builder.filtered_out?(mastered_submission)).to be false
        expect(query_builder.filtered_out?(not_mastered_submission)).to be false
        expect(query_builder.filtered_out?(not_completed_submission)).to be false
        expect(query_builder.filtered_out?(past_due_submission)).to be false
      end
    end

    context 'when filters is an empty array' do
      before do
        allow(query_builder).to receive(:filters).and_return([])
      end

      it 'returns false for any submission' do
        expect(query_builder.filtered_out?(mastered_submission)).to be false
        expect(query_builder.filtered_out?(not_mastered_submission)).to be false
        expect(query_builder.filtered_out?(not_completed_submission)).to be false
        expect(query_builder.filtered_out?(past_due_submission)).to be false
      end
    end

    context 'with single filter' do
      it 'filters out mastered submissions when master filter is active' do
        allow(query_builder).to receive(:filters).and_return(['master'])

        expect(query_builder.filtered_out?(mastered_submission)).to be true
        expect(query_builder.filtered_out?(not_mastered_submission)).to be false
        expect(query_builder.filtered_out?(not_completed_submission)).to be false
        expect(query_builder.filtered_out?(past_due_submission)).to be false
      end

      it 'filters out not_mastered submissions when not_master filter is active' do
        allow(query_builder).to receive(:filters).and_return(['not_master'])

        expect(query_builder.filtered_out?(mastered_submission)).to be false
        expect(query_builder.filtered_out?(not_mastered_submission)).to be true
        expect(query_builder.filtered_out?(not_completed_submission)).to be false
        expect(query_builder.filtered_out?(past_due_submission)).to be false
      end

      it 'filters out not_completed submissions when not_completed filter is active' do
        allow(query_builder).to receive(:filters).and_return(['not_completed'])

        expect(query_builder.filtered_out?(mastered_submission)).to be false
        expect(query_builder.filtered_out?(not_mastered_submission)).to be false
        expect(query_builder.filtered_out?(not_completed_submission)).to be true
        expect(query_builder.filtered_out?(past_due_submission)).to be false
      end

      it 'filters out past_due submissions when not_completed_past_due filter is active' do
        allow(query_builder).to receive(:filters).and_return(['not_completed_past_due'])

        expect(query_builder.filtered_out?(mastered_submission)).to be false
        expect(query_builder.filtered_out?(not_mastered_submission)).to be false
        expect(query_builder.filtered_out?(not_completed_submission)).to be false
        expect(query_builder.filtered_out?(past_due_submission)).to be true
      end
    end

    context 'with multiple filters' do
      it 'filters out submissions matching any of the filters' do
        allow(query_builder).to receive(:filters).and_return(%w[master not_completed])

        expect(query_builder.filtered_out?(mastered_submission)).to be true
        expect(query_builder.filtered_out?(not_mastered_submission)).to be false
        expect(query_builder.filtered_out?(not_completed_submission)).to be true
        expect(query_builder.filtered_out?(past_due_submission)).to be false
      end

      it 'filters out mastered and not_mastered submissions when both filters are active' do
        allow(query_builder).to receive(:filters).and_return(%w[master not_master])

        expect(query_builder.filtered_out?(mastered_submission)).to be true
        expect(query_builder.filtered_out?(not_mastered_submission)).to be true
        expect(query_builder.filtered_out?(not_completed_submission)).to be false
        expect(query_builder.filtered_out?(past_due_submission)).to be false
      end

      it 'filters out not_completed and past_due submissions when both filters are active' do
        allow(query_builder).to receive(:filters).and_return(%w[not_completed not_completed_past_due])

        expect(query_builder.filtered_out?(mastered_submission)).to be false
        expect(query_builder.filtered_out?(not_mastered_submission)).to be false
        expect(query_builder.filtered_out?(not_completed_submission)).to be true
        expect(query_builder.filtered_out?(past_due_submission)).to be true
      end

      it 'filters out all status types when all filters are active' do
        allow(query_builder).to receive(:filters).and_return(%w[master not_master not_completed not_completed_past_due])

        expect(query_builder.filtered_out?(mastered_submission)).to be true
        expect(query_builder.filtered_out?(not_mastered_submission)).to be true
        expect(query_builder.filtered_out?(not_completed_submission)).to be true
        expect(query_builder.filtered_out?(past_due_submission)).to be true
      end
    end

    context 'with complex submission statuses' do
      # Edge case: submission that doesn't cleanly fit into one category
      let(:ambiguous_submission) do
        instance_double(Submission,
                        master?: false,
                        not_master?: false,
                        not_completed?: false,
                        not_completed_past_due?: false,
                        requirement_status: 'unknown')
      end

      it 'does not filter out submissions that do not match any known status' do
        allow(query_builder).to receive(:filters).and_return(%w[master not_master not_completed not_completed_past_due])

        expect(query_builder.filtered_out?(ambiguous_submission)).to be false
      end
    end

    context 'with filters containing unknown values' do
      it 'ignores unknown filter values' do
        allow(query_builder).to receive(:filters).and_return(%w[unknown_filter master])

        expect(query_builder.filtered_out?(mastered_submission)).to be true
        expect(query_builder.filtered_out?(not_mastered_submission)).to be false
        expect(query_builder.filtered_out?(not_completed_submission)).to be false
        expect(query_builder.filtered_out?(past_due_submission)).to be false
      end
    end
  end
  describe '#records' do
    let!(:user) { create(:user, canvas_id: 123) }
    let!(:course) { create(:course, canvas_id: 456) }
    let!(:module1) { create(:context_module, course: course, canvas_context_type: 'Course') }
    let!(:module2) { create(:context_module, course: course, canvas_context_type: 'Course') }
    let!(:module_item1) { create(:context_module_item, canvas_id: 101, context_module: module1) }
    let!(:module_item2) { create(:context_module_item, canvas_id: 102, context_module: module2) }
    let(:course_progress_no_progress) do
      [
        {
          'course_id' => 1,
          'context_module_id' => 1,
          'context_module_item_id' => 101,
          'assignment_name' => 'assignment _1',
          'due_date' => nil,
          'completed_date' => nil,
          'graded_at' => nil,
          'points_possible' => nil,
          'score' => nil,
          'letter_grade' => nil,
          'submission_status' => nil,
          'content_type' => 'WikiPage',
          'content_id' => 1,
          'content_title' => 'assignment _1',
          'user_id' => 2,
          'module_progress_status' => nil,
          'todo_date' => nil,
          'lock_at' => nil,
          'requirements_met' => nil,
          'assignment_id' => 101,
          'requirement_type' => 'must_view',
          'requirement_met' => false
        },
        {
          'course_id' => 1,
          'context_module_id' => 2,
          'context_module_item_id' => 102,
          'assignment_name' => 'assignment _2',
          'due_date' => nil,
          'completed_date' => nil,
          'graded_at' => nil,
          'points_possible' => nil,
          'score' => nil,
          'letter_grade' => nil,
          'submission_status' => nil,
          'content_type' => 'WikiPage',
          'content_id' => 2,
          'content_title' => 'assignment _2',
          'user_id' => 2,
          'module_progress_status' => nil,
          'todo_date' => nil,
          'lock_at' => nil,
          'requirements_met' => nil,
          'assignment_id' => 102,
          'requirement_type' => 'must_view',
          'requirement_met' => false
        }
      ]
    end

    let(:course_progress_completed) do
      [
        {
          'course_id' => 1,
          'context_module_id' => 1,
          'context_module_item_id' => 1,
          'assignment_name' => 'assignment _12',
          'due_date' => Time.utc(2025, 9, 12, 5, 59, 0),
          'completed_date' => Time.utc(2025, 9, 11, 9, 58, 11, 709_029),
          'graded_at' => Time.utc(2025, 9, 11, 9, 58, 30, 229_928),
          'points_possible' => 10.0,
          'score' => 8.0,
          'letter_grade' => '8',
          'submission_status' => 'graded',
          'content_type' => 'Assignment',
          'content_id' => 2,
          'content_title' => 'assignment _12',
          'user_id' => 2,
          'module_progress_status' => 'completed',
          'todo_date' => nil,
          'lock_at' => nil,
          'requirements_met' => <<~YAML,
            ---
            - :id: 1
              :type: must_view
            - :id: 2
              :type: must_view
          YAML
          'assignment_id' => 1,
          'requirement_type' => 'must_view',
          'requirement_met' => true
        }
      ]
    end

    let!(:query_builder) { described_class.new({ canvas_user_id: user.canvas_id.to_i, canvas_course_id: course.canvas_id.to_i }) }

    before do
      allow(User).to receive(:find_by).with(canvas_id: user.canvas_id.to_i).and_return(user)
      allow(Course).to receive(:find_by).with(canvas_id: course.canvas_id.to_i).and_return(course)
      allow(Canvas).to receive(:with_shard).and_yield
      module1.update(completion_requirements: [{ 'id' => module_item1.canvas_id, 'type' => 'must_view' }])
      module2.update(completion_requirements: [{ 'id' => module_item2.canvas_id, 'type' => 'must_view' }])
    end

    context 'when student not made any progress' do
      before do
        allow(Canvas::Course).to receive(:course_module_progress).and_return(course_progress_no_progress)
      end

      it 'returns course details with all active module and items' do
        result = query_builder.records

        expect(result).to be_a(Hash)
        expect(result[:modules]).to be_an(Array)
        module_ids = result[:modules].map { |mod| mod[:module_id] }
        expect(module_ids).to include(module1.canvas_id)
        expect(module_ids).to include(module2.canvas_id)
        expect(result[:modules].size).to eq(2)

        # Check each module has items
        result[:modules].each do |mod|
          expect(mod[:items]).to be_an(Array)
          expect(mod['items_count']).to eq(mod[:items].count)
          # Check module progress status is present
          expect(mod).to have_key(:module_progress_status)
        end

        # Check course details keys
        expect(result).to include(
          :id,
          :canvas_id,
          :name,
          :course_code,
          :course_sis_id,
          :current_score,
          :current_letter_grade,
          :requirement_count,
          :requirement_completed_count,
          :past_due_requirements_count,
          :requirements_with_due_date_count,
          :course_progress_percent,
          :expected_course_progress,
          :expected_course_progress_percent,
          :color_code,
          :modules
        )

        expect(result[:show_fireworks]).to eq(false)
        expect(result[:current_score]).to be_nil
        expect(result[:current_letter_grade]).to be_nil
        expect(result[:requirement_count]).to eq(2)
        expect(result[:requirement_completed_count]).to eq(0)
        expect(result[:past_due_requirements_count]).to eq(0)
        expect(result[:requirements_with_due_date_count]).to eq(0)
        expect(result[:course_progress_percent]).to eq(0)
        expect(result[:expected_course_progress]).to eq(0)
        expect(result[:expected_course_progress_percent]).to eq(0)
        expect(result[:color_code]).to be_nil
      end
    end

    context 'when student has completed one module' do
      before do
        allow(Canvas::Course).to receive(:course_module_progress).and_return(course_progress_completed)
        # Clean up any existing celebration events
        CelebrationEvent.where(canvas_user_id: user.canvas_id, canvas_course_id: course.canvas_id).destroy_all
        module2.context_module_items.each do |item|
          ContextModuleProgression.create!(
            canvas_user_id: user.canvas_id,
            canvas_course_id: course.canvas_id,
            canvas_module_id: item.context_module.canvas_id,
            canvas_module_item_id: item.canvas_id,
            canvas_content_type: item.canvas_content_type,
            requirement_type: 'must_view',
            requirement_status: 'completed',
            module_progress_status: 'completed'
          )
        end
      end

      it 'return course details with show fireworks' do
        result = query_builder.records

        # pp result
        expect(result[:show_fireworks]).to eq(false)
        expect(result[:requirement_count]).to eq(1)
        expect(result[:requirement_completed_count]).to eq(1)
      end
    end
  end

  describe '#parse_canvas_date' do
    let(:query_builder) { described_class.new({}) }

    it 'returns nil for nil input' do
      expect(query_builder.send(:parse_canvas_date, nil)).to be_nil
    end

    it 'returns the same time for Time input' do
      now = Time.zone.now
      expect(query_builder.send(:parse_canvas_date, now)).to eq(now)
    end

    it 'parses valid time string' do
      str = '2025-09-12T05:59:00Z'
      parsed = query_builder.send(:parse_canvas_date, str)
      expect(parsed).to be_a(Time)
      expect(parsed.utc.to_s).to include('2025-09-12 05:59:00 UTC')
    end

    it 'returns nil for invalid time string' do
      expect(query_builder.send(:parse_canvas_date, 'not-a-time')).to be_nil
    end
  end

  describe '#calculate_score_percent' do
    let(:query_builder) { described_class.new({}) }

    it 'returns 0.0 when points_possible is zero' do
      expect(query_builder.send(:calculate_score_percent, 5, 0)).to eq(0.0)
    end

    it 'calculates percentage with two decimals' do
      expect(query_builder.send(:calculate_score_percent, 8, 10)).to eq(80.0)
      expect(query_builder.send(:calculate_score_percent, 5, 12)).to eq(41.67)
    end
  end

  describe '#fetch_letter_grade' do
    let(:query_builder) { described_class.new({}) }

    it 'returns nil when score is nil' do
      expect(query_builder.send(:fetch_letter_grade, nil)).to be_nil
    end

    it 'returns nil when grading scheme is nil' do
      allow(query_builder).to receive(:grading_scheme).and_return(nil)
      expect(query_builder.send(:fetch_letter_grade, 85)).to be_nil
    end

    it 'delegates to grading scheme score_to_grade' do
      scheme = instance_double(GradingScheme)
      allow(scheme).to receive(:score_to_grade).with(85).and_return({ 'name' => 'B' })
      allow(query_builder).to receive(:grading_scheme).and_return(scheme)
      expect(query_builder.send(:fetch_letter_grade, 85)).to eq('B')
    end
  end

  describe '#fetch_color_code' do
    let(:query_builder) { described_class.new({}) }

    it 'returns nil when score is nil' do
      expect(query_builder.send(:fetch_color_code, nil)).to be_nil
    end

    it 'returns nil when grading scheme colors is nil' do
      allow(query_builder).to receive(:grading_scheme_colors).and_return(nil)
      expect(query_builder.send(:fetch_color_code, 75)).to be_nil
    end

    it 'returns color_code from grading_scheme_colors.by_score' do
      color_obj = instance_double(AccountGradingSchemeColor, color_code: '#00FF00', default_color_code: nil)
      colors = double('grading_scheme_colors')
      allow(colors).to receive(:by_score).with(75).and_return(color_obj)
      allow(query_builder).to receive(:grading_scheme_colors).and_return(colors)
      expect(query_builder.send(:fetch_color_code, 75)).to eq('#00FF00')
    end

    it 'falls back to default_color_code' do
      color_obj = instance_double(AccountGradingSchemeColor, color_code: nil, default_color_code: '#AAAAAA')
      colors = double('grading_scheme_colors')
      allow(colors).to receive(:by_score).with(60).and_return(color_obj)
      allow(query_builder).to receive(:grading_scheme_colors).and_return(colors)
      expect(query_builder.send(:fetch_color_code, 60)).to eq('#AAAAAA')
    end
  end

  describe '#determine_requirement_status' do
    let(:query_builder) { described_class.new({}) }

    it 'returns submission.completed_status when requirement met from canvas' do
      submission = instance_double(Submission, completed_status: 'mastered', requirement_status: 'not completed')
      progress = { 'requirements_met' => "---\n- :id: 1\n", 'context_module_item_id' => 1 }
      allow(query_builder).to receive(:completed_requirement).and_return([:anything])
      allow(query_builder).to receive(:requirement_status).and_return('completed')
      expect(query_builder.send(:determine_requirement_status, progress, submission)).to eq('mastered')
    end

    it 'returns submission.requirement_status when submission present and not completed' do
      submission = instance_double(Submission, completed_status: 'mastered', requirement_status: 'not mastered')
      progress = { 'requirements_met' => nil }
      allow(query_builder).to receive(:requirement_status).and_return('incomplete')
      expect(query_builder.send(:determine_requirement_status, progress, submission)).to eq('not mastered')
    end

    it 'returns past due when incomplete and canvas todo_date is past' do
      progress = { 'todo_date' => 2.days.ago.iso8601 }
      allow(query_builder).to receive(:requirement_status).and_return('incomplete')
      expect(query_builder.send(:determine_requirement_status, progress, nil)).to eq('past due')
    end

    it 'returns past due when incomplete and canvas lock_at is past' do
      progress = { 'lock_at' => 1.day.ago.iso8601 }
      allow(query_builder).to receive(:requirement_status).and_return('incomplete')
      expect(query_builder.send(:determine_requirement_status, progress, nil)).to eq('not completed')
    end

    it 'returns not completed when incomplete and not past due' do
      progress = { 'todo_date' => 2.days.from_now.iso8601 }
      allow(query_builder).to receive(:requirement_status).and_return('incomplete')
      expect(query_builder.send(:determine_requirement_status, progress, nil)).to eq('not completed')
    end

    it 'returns completed when canvas says completed and no submission' do
      progress = { 'requirements_met' => "---\n- :id: 1\n", 'context_module_item_id' => 1 }
      allow(query_builder).to receive(:requirement_status).and_return('completed')
      expect(query_builder.send(:determine_requirement_status, progress, nil)).to eq('completed')
    end
  end

  describe 'expected course progress helpers' do
    let(:query_builder) { described_class.new({}) }

    it 'calculate_expected_course_progress_from_data returns 0 when no due-dated items' do
      data = { requirements_with_due_date_count: 0, past_due_requirements_count: 0 }
      expect(query_builder.send(:calculate_expected_course_progress_from_data, data)).to eq(0)
    end

    it 'returns 100 when there are any past due items' do
      data = { requirements_with_due_date_count: 5, past_due_requirements_count: 1 }
      expect(query_builder.send(:calculate_expected_course_progress_from_data, data)).to eq(100)
    end

    it 'returns 100 baseline when none are past due yet' do
      data = { requirements_with_due_date_count: 3, past_due_requirements_count: 0 }
      expect(query_builder.send(:calculate_expected_course_progress_from_data, data)).to eq(100)
    end

    it 'calculate_expected_course_progress_percent_from_data derives percent vs expected' do
      data = { requirement_count: 10, requirement_completed_count: 5, requirements_with_due_date_count: 3, past_due_requirements_count: 0 }
      expect(query_builder.send(:calculate_expected_course_progress_percent_from_data, data)).to eq(50)
    end

    it 'returns 0 percent when expected is zero' do
      data = { requirement_count: 5, requirement_completed_count: 2, requirements_with_due_date_count: 0, past_due_requirements_count: 0 }
      expect(query_builder.send(:calculate_expected_course_progress_percent_from_data, data)).to eq(0)
    end
  end

  describe '#generate_content_path' do
    let(:query_builder) { described_class.new({}) }

    it 'builds generic module item URL with course sharded id' do
      canvas_data = { 'context_module_item_id' => 999 }
      allow(query_builder).to receive(:course_sharded_id_string).and_return('123~456')
      expect(query_builder.generate_content_path(canvas_data)).to eq('courses/123~456/modules/items/999')
    end
  end

  describe '#course_sharded_id_string' do
    let(:query_builder) { described_class.new({}) }

    it 'returns sharded id when organization_shard_id present' do
      course = instance_double(Course, canvas_id: 789)
      query_builder.instance_variable_set(:@course, course)
      allow(query_builder).to receive(:organization_shard_id).and_return('1')
      allow(query_builder).to receive(:course_sharded_id).with(789).and_return('1~789')
      expect(query_builder.course_sharded_id_string).to eq('1~789')
    end

    it 'returns plain canvas_id when no org shard' do
      course = instance_double(Course, canvas_id: 789)
      query_builder.instance_variable_set(:@course, course)
      allow(query_builder).to receive(:organization_shard_id).and_return(nil)
      expect(query_builder.course_sharded_id_string).to eq('789')
    end
  end
end
