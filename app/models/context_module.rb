# frozen_string_literal: true

# # #
# AUTO GENERATED MIGRATION
# This migration was auto generated by the CanvasSync Gem.
# You can add new columns to this table, but removing or
# re-naming ones created here may break Canvas Syncing.
#

# A ContextModule is the same as a Canvas Module. They're called ContextModules for 2 reasons:
#   1 - Module is a reserved word in Rails and you can't call a model a Module
#   2 - Can<PERSON> calls them ContextModules
class ContextModule < ApplicationRecord
  include CanvasSync::Record
  include CanvasSync::Concerns::ApiSyncable
  # include CanvasSync::Concerns::LiveEventSync

  canvas_sync_features :defaults

  belongs_to :context, polymorphic: true, optional: true, primary_key: :canvas_id, foreign_key: :canvas_context_id, foreign_type: :canvas_context_type
  belongs_to :course, primary_key: :canvas_id, foreign_key: :canvas_context_id
  has_many :context_module_items, primary_key: :canvas_id, foreign_key: :canvas_context_module_id
  has_many :context_module_progressions, primary_key: :canvas_id, foreign_key: :canvas_module_id
  has_many :assignments, through: :context_module_items

  api_syncable({
                 canvas_id: :canvas_context_module_id,
                 canvas_context_id: :canvas_context_id,
                 canvas_context_type: :canvas_context_type,
                 position: :position,
                 name: :name,
                 workflow_state: :workflow_state,
                 unlock_at: :unlock_at,
                 completion_requirements: :completion_requirements,
                 prerequisites: :prerequisites,
                 requirement_count: :requirement_count
               }, lambda { |api|
                    context_module = api.course_module(canvas_context_id, canvas_id, { include: ['items'] })

                    if context_module.nil? # no context_module here means it is deleted in Canvas
                      Rails.logger.info("Context Module not found from canvas api: canvas_context_id=#{canvas_context_id}, canvas_id=#{canvas_id}")

                      self.workflow_state = :deleted

                      # HACK: Replaced `return self` with return attributes as it is expecting a hash to be returned, not an ActiveRecord object.
                      return attributes
                    end
                    context_module['workflow_state'] = context_module['published'] ? 'active' : 'unpublished'

                    # We are not using prerequisites so for now we can keep it blank, and later it will update via sync job
                    context_module['prerequisites'] = []

                    context_module['completion_requirements'] =
                      context_module['items']&.filter_map do |item|
                        next unless item['completion_requirement']

                        { id: item['id'] }.merge(item['completion_requirement'])
                      end
                    context_module
                  })
end
