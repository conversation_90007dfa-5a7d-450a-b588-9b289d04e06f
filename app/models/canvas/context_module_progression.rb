# frozen_string_literal: true

module Canvas
  class ContextModuleProgression < Canvas::BaseRecord
    self.table_name = 'context_module_progressions'

    belongs_to :context_module, class_name: 'Canvas::ContextModule', foreign_key: 'context_module_id'

    def self.formated_course_progress(course_ids)
      Canvas.with_shard do
        Canvas::Course
          .select(sql_selects)
          .joins(sql_joins)
          .where(id: course_ids).to_a
      end
    end

    def self.sql_joins # rubocop:disable Metrics/MethodLength
      <<~SQL.squish
        INNER JOIN "content_tags" ct
          ON ct.context_id = courses.id
          AND ct.context_type = 'Course'
          AND ct.context_module_id IS NOT NULL
        INNER JOIN "context_module_progressions" cmp
          ON cmp.context_module_id = ct.context_module_id
        LEFT JOIN "wiki_pages" wp
          ON wp.id = ct.content_id
          AND ct.content_type = 'WikiPage'
          AND wp.context_id = courses.id
          AND wp.context_type = 'Course'
        LEFT JOIN "discussion_topics" dt
          ON dt.id = ct.content_id
          AND ct.content_type = 'DiscussionTopic'
        INNER JOIN (
          SELECT
            cm.id AS cm_id,
            pr.requirement_id,
            trim(pr.requirement_type) AS requirement_type
          FROM "context_modules" cm,
            LATERAL (
              SELECT
                regexp_replace(requirement, E'.*:id: (\\\\d+).*', E'\\\\1')::INTEGER AS requirement_id,
                trim(regexp_replace(requirement, E'.*:type: ([a-z_]+)', E'\\\\1')) AS requirement_type
              FROM regexp_split_to_table(cm.completion_requirements, E'\\n- ') AS requirement
              WHERE requirement ~ ':id:' AND requirement ~ ':type:'
            ) pr
        ) parsed_requirements
          ON parsed_requirements.cm_id = ct.context_module_id
          AND parsed_requirements.requirement_id = ct.id
      SQL
    end

    def self.sql_selects
      <<~SQL.squish
        courses.id AS course_id,
        ct.context_module_id AS context_module_id,
        ct.id AS context_module_item_id,
        ct.content_type AS content_type,
        ct.content_id AS content_id,
        ct.title AS content_title,
        cmp.user_id AS user_id,
        cmp.workflow_state as module_progress_status,
        wp.url AS page_url,
        COALESCE(wp.todo_date, dt.todo_date) AS todo_date,
        dt.lock_at AS lock_at,
        cmp.requirements_met AS requirements_met,
        parsed_requirements.requirement_id AS requirement_id,
        parsed_requirements.requirement_type AS requirement_type
      SQL
    end
  end
end
