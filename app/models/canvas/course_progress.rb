# frozen_string_literal: true

module Can<PERSON>
  class CourseProgress < Canvas::BaseRecord
    self.abstract_class = true

    # Summary: { "total_requirements"=>12, "completed_requirements"=>7, "percent_complete"=>58.33 }
    def self.course_progress_summary(user_id, course_id) # rubocop:disable Metrics/MethodLength
      sql = <<~SQL
        WITH mods AS (
          SELECT cm.id AS context_module_id
          FROM context_modules cm
          WHERE cm.context_type = 'Course'
            AND cm.context_id = ?
        ),
        reqs AS (
          SELECT m.context_module_id,
                 regexp_count(
                   COALESCE(cm.completion_requirements, ''),
                   '(-[[:space:]]*:id:[[:space:]]*[0-9]+)'
                 ) AS req_count
          FROM mods m
          JOIN context_modules cm ON cm.id = m.context_module_id
        ),
        progress AS (
          SELECT cmp.context_module_id,
                 regexp_count(
                   COALESCE(cmp.requirements_met, ''),
                   '(-[[:space:]]*:id:[[:space:]]*[0-9]+)'
                 ) AS met_count
          FROM context_module_progressions cmp
          JOIN mods m ON m.context_module_id = cmp.context_module_id
          WHERE cmp.user_id = ?
        ),
        totals AS (
          SELECT
            COALESCE(SUM(reqs.req_count), 0)     AS total_requirements,
            COALESCE(SUM(progress.met_count), 0) AS completed_requirements
          FROM reqs
          LEFT JOIN progress USING (context_module_id)
        )
        SELECT
          total_requirements,
          completed_requirements,
          CASE
            WHEN total_requirements > 0
              THEN ROUND(100.0 * completed_requirements / total_requirements, 2)
            ELSE 0
          END AS percent_complete
        FROM totals;
      SQL

      run_sql(sql, course_id, user_id).first || {
        'total_requirements' => 0, 'completed_requirements' => 0, 'percent_complete' => 0.0
      }
    end

    # Breakdown: [{context_module_id, name, req_count, met_count, module_percent}, ...]
    def self.course_progress_breakdown(user_id, course_id) # rubocop:disable Metrics/MethodLength
      puts "current db: #{ActiveRecord::Base.connection.current_database}"
      sql = <<~SQL
        WITH mods AS (
          SELECT cm.id AS context_module_id, cm.name
          FROM context_modules cm
          WHERE cm.context_type = 'Course'
            AND cm.context_id = ?
        ),
        counts AS (
          SELECT
            m.context_module_id,
            m.name,
            regexp_count(
              COALESCE(cm.completion_requirements, ''),
              '(-[[:space:]]*:id:[[:space:]]*[0-9]+)'
            ) AS req_count
          FROM mods m
          JOIN context_modules cm ON cm.id = m.context_module_id
        ),
        met AS (
          SELECT
            m.context_module_id,
            regexp_count(
              COALESCE(cmp.requirements_met, ''),
              '(-[[:space:]]*:id:[[:space:]]*[0-9]+)'
            ) AS met_count
          FROM mods m
          LEFT JOIN context_module_progressions cmp
                 ON cmp.context_module_id = m.context_module_id
                AND cmp.user_id = ?
        )
        SELECT
          c.context_module_id,
          c.name,
          c.req_count,
          COALESCE(m.met_count, 0) AS met_count,
          CASE
            WHEN c.req_count > 0 THEN ROUND(100.0 * COALESCE(m.met_count, 0) / c.req_count, 2)
            ELSE 0
          END AS module_percent
        FROM counts c
        LEFT JOIN met m USING (context_module_id)
        ORDER BY c.name NULLS LAST, c.context_module_id;
      SQL

      run_sql(sql, course_id, user_id)
    end

    # Positional-binds helper: run_sql(sql, bind1, bind2, ...)
    def self.run_sql(sql, *binds)
      sanitized = send(:sanitize_sql_array, [sql, *binds])
      connection.exec_query(sanitized).to_a
    end
  end
end
