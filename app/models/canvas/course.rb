# frozen_string_literal: true

module <PERSON>vas
  class Course < Canvas::BaseRecord
    self.table_name = 'courses'

    class << self
      def progress_count(course_ids, user_id)
        sql = course_requirement_counts(course_ids, user_id)
        connection.exec_query(sql)
      end

      def users_todo_items(course_ids, user_id, start_date, end_date)
        base_sql = course_non_assignment_items(course_ids, user_id)
        sql = <<~SQL.squish
          #{base_sql}
          AND (todo_date BETWEEN '#{start_date}' AND '#{end_date}')
        SQL

        connection.exec_query(sql)
      end

      def users_only_past_due_todo_items(course_ids, user_id)
        base_sql = course_non_assignment_items(course_ids, user_id)
        sql = <<~SQL.squish
          #{base_sql}
          AND (todo_date < NOW()::date - INTERVAL '1 day')
          AND requirement_status != 'completed'
        SQL

        connection.exec_query(sql)
      end

      def course_requirement_counts(course_ids, user_id)
        <<~SQL.squish
          WITH course_reqs AS (
            #{user_courses_with_requirements(course_ids, user_id)}
          )
          SELECT id,
                 COUNT(*) AS req_count,
                 COUNT(completed_requirement_id) AS req_completed_count
          FROM course_reqs
          GROUP BY id;
        SQL
      end

      def course_non_assignment_items(course_ids, user_id)
        <<~SQL.squish
          WITH course_reqs AS (
            #{user_courses_with_requirements(course_ids, user_id)}
          ),
          course_items AS (#{course_requirement_with_content_detail_status})
          SELECT * FROM course_items
          WHERE content_type != 'Assignment'
        SQL
      end

      def course_requirement_with_content_detail_status
        <<~SQL.squish
          select course_reqs.id as course_id, course_reqs.name as course_name,
          ct.context_module_id AS context_module_id,
          ct.id AS context_module_item_id, ct.content_type AS content_type,
          ct.content_id AS content_id,
          ct.title AS content_title,
          COALESCE(wp.todo_date, dt.todo_date) AS todo_date,
          course_reqs.requirement_id, course_reqs.requirement_type,
          course_reqs.completed_requirement_id,
          case when course_reqs.completed_requirement_id IS NOT NULL THEN 'completed' ELSE 'incomplete' END as requirement_status
          from content_tags ct
          INNER JOIN course_reqs
          ON course_reqs.requirement_id = ct.id
          LEFT JOIN wiki_pages wp
            ON wp.id = ct.content_id
              AND ct.content_type = 'WikiPage'
          LEFT JOIN discussion_topics dt
            ON dt.id = ct.content_id
            AND ct.content_type = 'DiscussionTopic'
        SQL
      end

      def user_courses_with_requirements(course_ids, user_id)
        <<~SQL.squish
          SELECT
            courses.id, courses.name,
            course_req.id AS context_module_id,
            course_req.requirement_id,
            course_req.requirement_type,
            course_req.completion_requirements,
            completed_req.requirement_id AS completed_requirement_id,
            completed_req.requirement_type AS completed_requirement_type
          FROM courses
          INNER JOIN (
            #{context_module_requirement_extracted_sql(course_ids)}
          ) course_req
            ON course_req.context_id = courses.id
            AND course_req.workflow_state = 'active'
          LEFT JOIN (
            #{context_module_progression_extracted_sql}
          ) completed_req
            ON completed_req.context_module_id = course_req.id
            AND completed_req.user_id = #{user_id}
            AND completed_req.requirement_id = course_req.requirement_id
          WHERE courses.workflow_state IN ('active', 'available')
        SQL
      end

      def context_module_requirement_extracted_sql(course_ids)
        <<~SQL.squish
          SELECT
            cm.*,
            (regexp_match(chunk, ':\s*id:\s*(\\d+)'))[1]::int AS requirement_id,
            (regexp_match(chunk, ':\s*type:\s*(\\w+)'))[1]   AS requirement_type
          FROM context_modules cm
          CROSS JOIN LATERAL regexp_split_to_table(cm.completion_requirements::text, E'\\n-') AS chunk
          WHERE cm.context_type = 'Course'
            AND cm.context_id IN (#{course_ids.join(',')})
            AND chunk ~ ':\s*id:'
        SQL
      end

      def context_module_progression_extracted_sql
        <<~SQL.squish
          SELECT
            cmp.*,
            (regexp_match(chunk, ':\s*id:\s*(\\d+)'))[1]::int AS requirement_id,
            (regexp_match(chunk, ':\s*type:\s*(\\w+)'))[1]   AS requirement_type
          FROM context_module_progressions cmp
          CROSS JOIN LATERAL regexp_split_to_table(cmp.requirements_met::text, E'\\n-') AS chunk
          WHERE chunk ~ ':\s*id:'
        SQL
      end

      def course_module_progress(course_id, user_id)
        sql = <<~SQL.squish
          SELECT
            #{canvas_sql_selects}
          FROM courses
          #{canvas_sql_joins(course_id, user_id)}
          WHERE courses.id = #{course_id}
            AND cmp.user_id = #{user_id}
        SQL
        connection.exec_query(sql).to_a
      end

      def canvas_sql_joins(course_id, user_id)
        <<~SQL.squish
          INNER JOIN content_tags ct
            ON ct.context_id = courses.id
            AND ct.context_type = 'Course'
            AND ct.context_module_id IS NOT NULL
          LEFT JOIN submissions s
            ON s.assignment_id = ct.content_id
            AND ct.content_type = 'Assignment'
            AND s.user_id = #{user_id}
          INNER JOIN context_module_progressions cmp
            ON cmp.context_module_id = ct.context_module_id
          LEFT JOIN wiki_pages wp
            ON wp.id = ct.content_id
            AND ct.content_type = 'WikiPage'
            AND wp.context_id = courses.id
            AND wp.context_type = 'Course'
          LEFT JOIN discussion_topics dt
            ON dt.id = ct.content_id
            AND ct.content_type = 'DiscussionTopic'
          INNER JOIN (
            #{context_module_requirement_extracted_sql([course_id])}
          ) parsed_requirements
            ON parsed_requirements.id = ct.context_module_id
            AND parsed_requirements.requirement_id = ct.id
        SQL
      end

      def canvas_sql_selects
        <<~SQL.squish
          courses.id AS course_id,
          ct.context_module_id AS context_module_id,
          ct.id AS context_module_item_id,
          ct.content_type AS content_type,
          ct.content_id AS content_id,
          ct.title AS title,
          cmp.user_id AS user_id,
          cmp.workflow_state as module_progress_status,
          COALESCE(wp.todo_date, dt.todo_date) AS todo_date,
          s.cached_due_date AS assignment_due_date,
          dt.lock_at AS lock_at,
          cmp.requirements_met AS requirements_met,
          parsed_requirements.requirement_id AS assignment_id,
          parsed_requirements.requirement_type AS requirement_type,
          CASE
            WHEN cmp.requirements_met ~ (':id:\\s*' || ct.id) THEN TRUE
            ELSE FALSE
          END AS requirement_met
        SQL
      end
    end
  end
end
