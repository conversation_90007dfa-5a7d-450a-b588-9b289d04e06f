# frozen_string_literal: true

module <PERSON><PERSON>
  def self.with_shard
    config = current_organization&.settings&.dig(:canvas_replica)
    raise "Canvas shard configuration missing for organization #{current_organization&.id || 'unknown'}" unless config

    Canvas::BaseRecord.establish_connection(
      adapter: 'postgresql',
      host: config[:host],
      port: config[:port],
      database: config[:dbname],
      username: config[:user],
      password: config[:password],
      encoding: 'unicode',
      pool: 5,
      timeout: 5000
    )

    yield
  ensure
    Canvas::BaseRecord.remove_connection
  end
end
