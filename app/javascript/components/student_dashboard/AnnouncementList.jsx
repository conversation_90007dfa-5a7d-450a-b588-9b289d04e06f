import { View } from '@instructure/ui-view';
import { Text } from '@instructure/ui-text';
import { Alert } from '@instructure/ui-alerts';
import {
  IconInfoLine,
  IconWarningLine,
  IconQuestionLine,
  IconCalendarMonthLine
} from '@instructure/ui-icons';

import * as API from "../../utils/api";

const AnnouncementsList = ({ studentId, announcements = [], onAnnouncementsChange, onLoadingChange }) => {
  const handleDismissAnnouncement = (announcementId) => {
    if (onLoadingChange) onLoadingChange(true);
    API.dismissAnnouncement(studentId, announcementId)
      .then(() => {
        // Remove the dismissed announcement from the local state
        const updatedAnnouncements = announcements.filter(announcement => announcement.id !== announcementId);
        if (onAnnouncementsChange) onAnnouncementsChange(updatedAnnouncements);
        if (onLoadingChange) onLoadingChange(false);
      })
      .catch(error => {
        console.error('Failed to dismiss announcement', error);
        if (onLoadingChange) onLoadingChange(false);
      });
  };

  const getAnnouncementIcon = (iconType) => {
    switch (iconType) {
      case 'information':
        return <IconInfoLine />;
      case 'warning':
        return <IconWarningLine />;
      case 'error':
        return <IconWarningLine />;
      case 'question':
        return <IconQuestionLine />;
      case 'calendar':
        return <IconCalendarMonthLine />;
      default:
        return <IconInfoLine />;
    }
  };

  const getAnnouncementVariant = (iconType) => {
    switch (iconType) {
      case 'error':
        return 'error';
      case 'warning':
        return 'warning';
      case 'information':
      case 'question':
      case 'calendar':
      default:
        return 'info';
    }
  };

  const renderAnnouncements = () => {
    return announcements.map((announcement, index) => (
      <View
        key={`announcement-wrapper-${index}`}
        margin="0 0 small 0"
        maxWidth="880px"
        display="block"
      >
        <Alert
          variant={getAnnouncementVariant(announcement.icon)}
          renderCloseButtonLabel="Dismiss announcement"
          renderCustomIcon={() => getAnnouncementIcon(announcement.icon)}
          onDismiss={() => handleDismissAnnouncement(announcement.id)}
        >
          <Text size="medium" weight="bold">{announcement.subject}</Text>
          <View
            as="div"
            textAlign="start"
            lineHeight="condensed"
            dangerouslySetInnerHTML={{ __html: announcement.message }}
          />
        </Alert>
      </View>
    ));
  };

  return (
    <View as="div" minWidth="0">
      {renderAnnouncements()}
    </View>
  );
};

export default AnnouncementsList;
