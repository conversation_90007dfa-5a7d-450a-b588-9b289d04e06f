import { useEffect, useState } from 'react';
import { Route, MemoryRouter as Router, Routes } from 'react-router-dom'

import { Tabs } from '@instructure/ui-tabs';
import { View } from '@instructure/ui-view';
import { Text } from '@instructure/ui-text';
import { Heading } from '@instructure/ui-heading';
import { Flex } from '@instructure/ui-flex';
import moment from 'moment-timezone';
import DayView from '../agenda/DayView';
import WeekView from '../agenda/WeekView';
import PastDues from '../agenda/PastDues';
import * as API from "../../utils/api";
import { TabTitle, TextWithBadgeCount } from '../../shared/components/UtilUI';
import { useTheme } from '../../shared/contexts/ThemeContext';

const AgendaTab = (props) => {
  const { studentId, isK5Student = false} = props;
  const [selectedTab, setSelectedTab] = useState('day_view');
  const [assignments, setAssignments] = useState([]);
  const [bubbleMessage, setBubbleMessage] = useState('');
  const { currentTheme: contextTheme, themeAssets: contextThemeAssets } = useTheme();

  const currentTheme = isK5Student ? contextTheme : 'no_theme';

  const defaultNoThemeAssets = {
    background: {
      course: null,
      announcement: null,
      resource: null,
      teacher: null,
    },
    elements: {},
    colors: {
      accent: '#C485FD',
      background: '#F5F5F5',
      primary: '#A41D95',
      secondary: '#7FC3AE',
    },
  };

  // Use default assets if currentTheme is 'no_theme', else use context assets
  const themeAssets = currentTheme === 'no_theme' ? defaultNoThemeAssets : contextThemeAssets;

  const handleTabChange = (data) => {
    setSelectedTab(data.id)
  }

  useEffect(() => {
    getDayDueAssignments();
  }, [studentId]);

  const getDayDueAssignments = () => {
    API.getStudentDayDueAssignments(studentId, {only_past_dues: true})
      .then((response) => response.data)
      .then((response) => {
        setAssignments(response.assignments);
      })
      .catch((error) => {
        console.log(error);
      });
  }

  const renderTitle = () => {
    if (selectedTab === 'past_due_view') {
      return <TabTitle title='Past Due' isSelected={true} />
    } else {
      return <TextWithBadgeCount title='Past Due' count={assignments.length} />
    }
  }

  const renderAgendaHeader = () => {
    // Use moment-timezone to format the date in the user's timezone
    const formattedDate = moment().tz(window.ENV.user_config?.timezone ?? 'UTC').format('dddd, MMMM D, YYYY');

    return (
      <View as="div" padding="small" className="header-text">
        <Flex justifyItems="space-between" alignItems="center">
          <Flex.Item >
            <Heading level="h2" margin="0" >
              <Text size="x-large" weight="bold">My Agenda</Text>
            </Heading>
          </Flex.Item>
          <Flex.Item>
            <Text size="medium" weight="normal">
              <strong>Today's Date:</strong> {formattedDate}
            </Text>
          </Flex.Item>
        </Flex>
      </View>
    );
  }

  // Helper to get theme style for bubble messages
  const getThemeStyle = (theme) => {
    const themeStyleMap = {
      forest: { bottom: '433px', left: '230px' },
      winter: { bottom: '530px', left: '30px' },
      space: { bottom: '600px', left: '20px' },
    };
    return themeStyleMap[theme] || {};
  };

  const getPastDueThemeStyle = (theme) => {
    const themeStyleMap = {
      forest: { left: '350px' },
      winter: { left: '190px' },
      space: { left: '190px' },
    };
    return themeStyleMap[theme] || {};
  };

  // Helper to render theme images
  const renderThemeImage = ({ type, theme, elements, hasAssignments }) => {
    if (!elements) return null;
    if (type === 'day_view') {
      if (!elements.foxWave) return null;
      const style =
        theme === 'forest'
          ? { maxWidth: '600px' }
          : { maxWidth: '450px', marginTop: '85px' };
      return (
        <img
          src={elements.foxWave}
          className={`theme-element theme-fox fox-wave fox-wave-${theme}`}
          alt="Fox Wave"
          key={`foxWave-${theme}`}
          style={style}
        />
      );
    } else if (type === 'pastdue') {
      if (hasAssignments && elements.foxOverdue) {
        const style =
          theme === 'forest'
            ? { maxWidth: '600px' }
            : { maxWidth: '450px', marginTop: '85px' };
        return (
          <img
            src={elements.foxOverdue}
            className={`theme-element theme-fox fox-overdue fox-overdue-${theme}`}
            alt="Fox Overdue"
            key={`foxOverdue-${theme}`}
            style={style}
          />
        );
      }
      if (!hasAssignments) {
        const imgSrc =
          theme === 'forest'
            ? elements.PastdueFoxWave
            : elements.foxWave;
        if (!imgSrc) return null;
        const style =
          theme === 'forest'
            ? { maxWidth: '600px' }
            : { maxWidth: '450px', marginTop: '85px' };
        return (
          <img
            src={imgSrc}
            className={`theme-element theme-fox fox-wave fox-wave-${theme}`}
            alt="Fox Wave"
            key={`foxWave-${theme}`}
            style={style}
          />
        );
      }
    }
    return null;
  };

  const renderThemeElements = () => {
    if (!isK5Student || selectedTab === 'week_view' || !themeAssets?.elements || !currentTheme) return null;
    const img = renderThemeImage({ type: 'day_view', theme: currentTheme, elements: themeAssets.elements });
    if (!img) return null;
    return (
      <View as="div" className="theme-elements-wrapper">
        {img}
      </View>
    );
  };

  const renderBubbleMessage = () => {
    if (!isK5Student || !bubbleMessage || !currentTheme) return null;
    return (
      <View
        as="div"
        className="bubble-message"
        style={{ ...getThemeStyle(currentTheme) }}
      >
        <Text>{bubbleMessage}</Text>
      </View>
    );
  };

  const renderPastdueThemeElements = () => {
    if (!isK5Student || selectedTab === 'week_view' || !themeAssets?.elements || !currentTheme) return null;
    const hasAssignments = assignments?.length > 0;
    const img = renderThemeImage({
      type: 'pastdue',
      theme: currentTheme,
      elements: themeAssets.elements,
      hasAssignments,
    });
    if (!img) return null;
    return (
      <View as="div" className="theme-elements-wrapper">
        {img}
      </View>
    );
  };

  const renderPastdueBubbleMessage = () => {
    if (!isK5Student || !currentTheme) return null;
    const hasAssignments = assignments?.length > 0;
    const message = hasAssignments ? `Contact your teacher for help catching up.` : `Great work! You have no Past Due items!`;
    return (
      <View
        as="div"
        className="bubble-message"
        style={{ ...getPastDueThemeStyle(currentTheme) }}
      >
        <Text>{message}</Text>
      </View>
    );
  };

  const getContentWidth = () => {
    return currentTheme !== "no_theme" && isK5Student && selectedTab !== 'week_view' ? '60%' : '100%';
  };

  const renderThemeAndMessage = () => {
    if (selectedTab === 'day_view') {
      return (
        <>
          {renderBubbleMessage()}
          {renderThemeElements()}
        </>
      );
    }
    if (selectedTab === 'past_due_view') {
      return (
        <>
          {renderPastdueBubbleMessage()}
          {renderPastdueThemeElements()}
        </>
      );
    }
    return null;
  };

  return (
    <View
      as="div"
      height="100%"
      width="100%"
    >
      <View as="div" width="100%">
        {renderAgendaHeader()}
      </View>
      <View
        as="div"
        height="100%"
        width={getContentWidth()}
        className="agenda-tab-content"
      >
        <Tabs
          variant="secondary"
          margin="small 0"
          padding="0"
          onRequestTabChange={(event, data) => handleTabChange(data)}
        >
          <Tabs.Panel
            key="day_view"
            id="day_view"
            renderTitle={<TabTitle title="Daily Plan" isSelected={selectedTab === 'day_view'} />}
            textAlign="center"
            padding="xx-small 0 0 0"
            isSelected={selectedTab === 'day_view'}
          >
            <DayView
              studentId={studentId}
              setBubbleMessage={setBubbleMessage}
              themeAssets={themeAssets}
              currentTheme={currentTheme}
              isK5Student={isK5Student}
              hasPastDueItems={assignments && assignments.length > 0}
              setSelectedTab={setSelectedTab}
            />
          </Tabs.Panel>
          <Tabs.Panel
            key="week_view"
            id="week_view"
            renderTitle={<TabTitle title="Weekly Plan" isSelected={selectedTab === 'week_view'} />}
            textAlign="center"
            padding="xx-small 0 0 0"
            isSelected={selectedTab === 'week_view'}
          >
            <WeekView studentId={studentId} />
          </Tabs.Panel>
          <Tabs.Panel
            key="past_due_view"
            id="past_due_view"
            renderTitle={renderTitle}
            textAlign="center"
            padding="xx-small 0 0 0"
            isSelected={selectedTab === 'past_due_view'}
          >
            <PastDues studentId={studentId} dueAssignments={assignments} />
          </Tabs.Panel>
        </Tabs>
      </View>
      <div className="renderBubbleMessage">
        {renderThemeAndMessage()}
      </div>
    </View>
  )
}

export default AgendaTab
