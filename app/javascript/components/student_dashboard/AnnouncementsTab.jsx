import { useState } from 'react';
import { View } from '@instructure/ui-view';
import { Text } from '@instructure/ui-text';
import { Flex } from '@instructure/ui-flex';
import { Link } from '@instructure/ui-link';
import { Heading } from '@instructure/ui-heading';
import { Spinner } from '@instructure/ui-spinner';
import { InfoAlert } from '../../shared/components/UtilUI';
import AnnouncementsList from './AnnouncementList';

const AnnouncementsTab = (props) => {
  const { studentId, announcements = [], onAnnouncementsChange } = props;
  const [isLoading, setIsLoading] = useState(false);

  const renderNotificationSettings = () => {
    const canvasUrl = window.location.ancestorOrigins?.[0] || window.ENV?.canvas_url;
    const notificationPreferencesUrl = canvasUrl ? `${canvasUrl}/profile/communication` : '#';
    const globalAnnouncementsUrl = canvasUrl ? `${canvasUrl}/account_notifications` : '#';

    return (
      <View
        as="div"
        background="secondary"
        padding="small"
        borderRadius="medium"
        borderWidth="small"
        width="40%"
        margin='none none none xx-large'
        themeOverride={{
          backgroundSecondary: '#ffffff',
        }}
      >

        <Heading level="h4" margin="0 0 small 0">Notification Settings</Heading>
        <Text as="div" margin="0 0 small 0" size="small">
          Tell us how and when you would like to be notified of events in Canvas.
        </Text>
        <View as="div" margin="0 0 small 0" size="medium">
          <Link href={notificationPreferencesUrl} target="_parent" isWithinText={true} size='small'>
            Set Canvas Notification Preferences
          </Link>
        </View>
        <View as="div">
          <Link href={globalAnnouncementsUrl} target="_parent" isWithinText={false} size='small'>
            View All Global Announcements in Canvas
          </Link>
        </View>
      </View>
    );
  };

  const renderSpinner = () => {
    return (
      <Flex justifyItems="center" alignItems="center" height="100%">
        <Spinner renderTitle="Loading announcements..." size="medium" />
      </Flex>
    );
  };

  const renderEmptyState = () => {
    return (
      <InfoAlert
        subText="You have no un-dismissed Global Announcements at this time. Course announcements are accessible within your Canvas courses."
      />
    );
  };

  const renderAnnouncementContent = () => {
    if (isLoading) {
      return renderSpinner();
    }

    if (announcements.length === 0) {
      return renderEmptyState();
    }

    return (
      <AnnouncementsList
        studentId={studentId}
        announcements={announcements}
        onAnnouncementsChange={(updatedAnnouncements) => {
          onAnnouncementsChange(updatedAnnouncements);
          setIsLoading(false);
        }}
        onLoadingChange={setIsLoading}
      />
    );
  };


  return (
    <View as="div" padding="medium" textAlign="start" className='header-text' overflowX="hidden !important" height="100%">
      <Text size="large" weight="bold" >Global Announcements </Text>({announcements.length})
      <Flex direction="row" alignItems="start" gap="medium">
        <Flex.Item size="50%" shouldShrink>
          <View as="div" minWidth="0">
            {renderAnnouncementContent()}
          </View>
        </Flex.Item>
        <Flex.Item size="50%" shouldShrink margin="none none none xx-large">
          {renderNotificationSettings()}
        </Flex.Item>
      </Flex>
    </View>
  );
}

export default AnnouncementsTab;
