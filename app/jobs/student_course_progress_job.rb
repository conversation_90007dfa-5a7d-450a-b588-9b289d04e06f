# frozen_string_literal: true

class StudentCourseProgressJob < CanvasSync::Job
  queue_as :critical

  def perform(options = { course_id: nil, student_id: nil })
    start_time = Time.current
    Rails.logger.info "Starting StudentCourseProgressJob at: #{start_time}"
    return unless options[:course_id].present?
    return unless options[:student_id].present?

    # course_id = 3577
    course_id = options[:course_id]
    student_id = options[:student_id]
    course = Course.find_by(canvas_id: course_id)
    return unless course

    Rails.logger.info "Found course with canvas_id: #{course.canvas_id} for student with canvas_user_id: #{student_id}"
    process_course_modules(course, student_id)
    total_duration = Time.current - start_time
    Rails.logger.info "Completed StudentCourseProgressJob in #{total_duration.round(2)}s"
  end

  private

  def process_course_modules(course, student_id)
    course_id = course.canvas_id
    Rails.logger.info "Processing course with canvas_id: #{course_id} for student with canvas_user_id: #{student_id}"
    course_module_start_time = Time.current
    modules = fetch_course_modules(course_id, student_id)
    course_module_duration = Time.current - course_module_start_time
    Rails.logger.info "StudentCourseProgressJob fetch_course_modules, student #{student_id} duration in #{course_module_duration.round(2)}s"
    module_progress_start_time = Time.current
    return unless modules.present?

    sync_student_module_progress(modules, course_id, student_id)
    module_progress_duration = Time.current - module_progress_start_time
    Rails.logger.info "StudentCourseProgressJob sync_student_module_progress, student #{student_id} duration in #{module_progress_duration.round(2)}s"
  rescue StandardError => e
    Rails.logger.error "Error process_course_modules : #{e.message}"
    Rails.logger.error e.backtrace.join("\n")
    raise e
  end

  def sync_student_module_progress(modules, course_id, student_id)
    modules.each do |module_data|
      module_id = module_data['id']
      module_progress_status = module_data['state']
      module_data['items'].each do |item|
        save_progression(module_id, item, course_id, student_id, module_progress_status)
      rescue StandardError => e
        Rails.logger.error "Error sync_student_module_progress, item #{item['id']}, student #{student_id}: #{e.message}"
      end
    end
  end

  def save_progression(module_id, item, course_id, student_id, module_progress_status)
    progression = ContextModuleProgression.find_or_initialize_by(
      canvas_module_id: module_id,
      canvas_module_item_id: item['id'],
      canvas_user_id: student_id
    )

    progression.assign_attributes(
      canvas_course_id: course_id,
      canvas_content_type: item['type'],
      canvas_content_id: item['content_id'],
      canvas_content_title: item['title'],
      canvas_page_url: item['page_url'],
      requirement_type: item.dig('completion_requirement', 'type'),
      requirement_status: item.dig('completion_requirement', 'completed') ? 'completed' : 'incomplete',
      lock_at: item.dig('content_details', 'lock_at'),
      due_at: item.dig('content_details', 'due_at'),
      module_progress_status: module_progress_status
    )

    progression.save! if progression.changed?
  end

  def fetch_course_modules(course_id, student_id)
    canvas_sync_client.get(
      "/api/v1/courses/#{course_id}/modules/",
      include: %w[items content_details],
      student_id: student_id,
      as_user_id: student_id
    ).all_pages!
  rescue Footrest::HttpError::NotFound => e
    Rails.logger.warn "Module not found for course #{course_id}, student #{student_id}: #{e.message}"
    nil
  rescue Footrest::HttpError::Unauthorized => e
    Rails.logger.warn "Unauthorized access for course #{course_id}, student #{student_id}: #{e.message}"
    nil
  end
end
