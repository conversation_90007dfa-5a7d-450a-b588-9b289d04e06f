# frozen_string_literal: true

module QueryBuilder
  class StudentDueAssignments < BaseQueryBuilder
    def self.across_shard(student, options = {})
      all_requirements = []
      student.against_shards do |shard_student|
        requirements = StudentDueAssignments.new({
                                                   canvas_user_id: shard_student.canvas_id,
                                                   organization: current_organization,
                                                   filters: options
                                                 }).records
        all_requirements += requirements
      end
      all_requirements
    end

    def records
      all_items = []
      canvas_course_ids = student_uniq_course_enrollment_ids.map(&:canvas_course_id)
      all_items += fetch_assignment_items_from_lti
      non_assignment_items = fetch_non_assignment_items_from_canvas(canvas_course_ids, user_id)
      all_items += non_assignment_items_formated_json(non_assignment_items)
      all_items
    end

    def sql_selects
      <<~SQL.squish
        #{basic_selects},
        #{requirement_status_case_sql} as req_status,
        #{organization_selects}
      SQL
    end

    private

    def fetch_non_assignment_items_from_canvas(canvas_course_ids, user_id)
      Canvas.with_shard do
        if filters[:only_past_dues].present?
          Canvas::Course.users_only_past_due_todo_items(canvas_course_ids, user_id)
        else
          start_date, end_date = day_date_range if filters[:due_date].present? && !filters[:weekly].present?
          start_date, end_date = weekly_date_range if filters[:due_date].present? && filters[:weekly].present?
          Canvas::Course.users_todo_items(canvas_course_ids, user_id, start_date, end_date)
        end
      end
    end

    def non_assignment_items_formated_json(non_assignment_items)
      # Format the result into an array of hashes
      items = []
      non_assignment_items.each do |row|
        non_assignment_item = {
          id: row['context_module_item_id'],
          item_type: row['content_type'],
          canvas_assignment_id: nil,
          assignment_title: row['content_title'],
          assignment_due_at: nil,
          submission_due_at: nil,
          todo_date: row['todo_date'],
          canvas_course_id: row['course_id'],
          course_name: row['course_name'],
          submission_score: nil,
          submission_points_possible: nil,
          submission_state: nil,
          requirement_status: row['requirement_status'],
          requirement_type: row['requirement_type'],
          req_status: non_assignment_item_display_status(row),
          organization_id: organization_id,
          organization_name: organization_name,
          organization_base_url: organization_base_url,
          organization_shard_id: organization_shard_id
        }.with_indifferent_access
        urls = generate_content_urls(non_assignment_item, row['course_id'])
        items << non_assignment_item.merge(urls)
      end
      items
    end

    def non_assignment_item_display_status(row)
      case row['requirement_status']
      when 'completed'
        'mastered'
      when 'incomplete'
        'not completed'
      else
        'not started'
      end
    end

    def fetch_assignment_items_from_lti
      # Get all module items with their progressions (both completed and incomplete)
      result = Course.selectable.joins(sql_joins)
                     .select(sql_selects)
                     .where(sql_where_conditions)
                     .order(Arel.sql(sql_order_clause))

      # Add Canvas URLs and paths for all content types
      result.map do |row|
        canvas_course_id = if row.organization_shard_id != 1 && row.canvas_course_id < PandaPal::Organization::SHARD_OFFSET
                             (PandaPal::Organization::SHARD_OFFSET * row.organization_shard_id) + row.canvas_course_id
                           else
                             row.canvas_course_id
                           end

        # Generate appropriate URLs based on content type
        urls = generate_content_urls(row, canvas_course_id)
        row.attributes.merge(urls)
      end
    end

    def basic_selects
      <<~SQL.squish
        context_module_items.canvas_id AS id,
        context_module_items.canvas_content_type AS item_type,
        context_module_items.canvas_assignment_id,
        COALESCE(assignments.title, 'Untitled Item') AS assignment_title,
        COALESCE(assignments.due_at) AS assignment_due_at,
        COALESCE(submissions.due_at, assignments.due_at) AS submission_due_at,
        courses.canvas_id AS canvas_course_id,
        courses.name AS course_name,
        courses.sis_id AS course_sis_id,
        courses.course_code AS course_code,
        courses.workflow_state AS course_state,
        submissions.score AS submission_score,
        submissions.points_possible AS submission_points_possible,
        submissions.workflow_state AS submission_state,
        c_r.req_type AS requirement_type,
        ( CASE
          WHEN context_module_items.canvas_assignment_id IS NOT NULL AND submissions.workflow_state IN ('graded', 'submitted') THEN 'completed'
          ELSE 'incomplete'
          END ) AS requirement_status
      SQL
    end

    def requirement_status_case_sql
      <<~SQL.squish
        ( CASE
          WHEN context_module_items.canvas_assignment_id IS NOT NULL AND submissions.workflow_state IN ('graded', 'submitted') AND COALESCE((submissions.score / NULLIF(submissions.points_possible,0))*100,0) > 80 THEN 'mastered'
          WHEN context_module_items.canvas_assignment_id IS NOT NULL AND submissions.workflow_state IN ('graded', 'submitted') AND COALESCE((submissions.score / NULLIF(submissions.points_possible,0))*100,0) <= 80 THEN 'not mastered'
          WHEN context_module_items.canvas_assignment_id IS NOT NULL AND submissions.workflow_state IN ('graded', 'submitted') THEN 'not mastered'
          WHEN context_module_items.canvas_assignment_id IS NOT NULL AND submissions.workflow_state IN ('submitted') THEN 'submitted'
          WHEN context_module_items.canvas_assignment_id IS NOT NULL AND submissions.workflow_state IS NOT NULL THEN 'not started'
          ELSE 'not started'
          END )
      SQL
    end

    def organization_selects
      <<~SQL.squish
        '#{organization_id}' AS organization_id,
        '#{organization_name}' AS organization_name,
        '#{organization_base_url}' AS organization_base_url,
        #{organization_shard_id} AS organization_shard_id
      SQL
    end

    def sql_joins
      <<~SQL.squish
        INNER JOIN #{Enrollment.quoted_table_name} enrollments
          ON enrollments.canvas_user_id = #{user_id}
          AND enrollments.canvas_course_id = courses.canvas_id
          AND enrollments.base_role_type = 'StudentEnrollment' AND enrollments.workflow_state = 'active'
        INNER JOIN (#{context_modules_sql}) c_r ON c_r.canvas_context_id = courses.canvas_id AND c_r.canvas_context_type = 'Course' AND c_r.workflow_state = 'active'
        INNER JOIN context_module_items ON context_module_items.canvas_id = c_r.req_id AND context_module_items.workflow_state = 'active'
        LEFT JOIN assignments ON assignments.canvas_id = context_module_items.canvas_assignment_id
        LEFT JOIN submissions ON submissions.canvas_assignment_id = assignments.canvas_id AND submissions.canvas_user_id = #{user_id}
      SQL
    end

    def generate_content_urls(row, canvas_course_id)
      # Use generic module item path for all content types
      item_id = needs_sharding?(row, row['id']) ? "#{row['organization_shard_id']}~#{row['id']}" : row['id']
      path = "courses/#{canvas_course_id}/modules/items/#{item_id}"

      {
        'course_content_path' => path
      }
    end

    def needs_sharding?(row, id)
      row['organization_shard_id'] != 1 && id.to_i < PandaPal::Organization::SHARD_OFFSET
    end

    def sql_where_conditions
      conditions = []

      # Past due filter
      conditions << past_due_condition if filters[:only_past_dues].present?

      # Date filter
      conditions << date_filter_condition if filters[:due_date].present? && !filters[:weekly].present?

      # Weekly filter
      conditions << weekly_filter_condition if filters[:weekly].present? && filters[:due_date].present?

      conditions.any? ? conditions.join(' AND ') : '1=1'
    end

    def sql_order_clause
      <<~SQL.squish
        COALESCE(submissions.due_at, assignments.due_at) ASC NULLS LAST
      SQL
    end

    def past_due_condition
      <<~SQL.squish
        (
          COALESCE(submissions.due_at, assignments.due_at) < NOW()::date - INTERVAL '1 day'
          AND #{requirement_status_case_sql} NOT IN ('mastered', 'completed')
        )
      SQL
    end

    def date_filter_condition
      start_of_day, end_of_day = day_date_range
      <<~SQL.squish
        COALESCE(submissions.due_at, assignments.due_at)
        BETWEEN '#{start_of_day}' AND '#{end_of_day}'
      SQL
    end

    def weekly_filter_condition
      start_date, end_date = weekly_date_range
      <<~SQL.squish
        COALESCE(submissions.due_at, assignments.due_at)
        BETWEEN '#{start_date}' AND '#{end_date}'
      SQL
    end

    def day_date_range
      target_date = Time.zone.parse(filters[:due_date])
      start_of_day = target_date.beginning_of_day.utc
      end_of_day = target_date.end_of_day.utc
      [start_of_day, end_of_day]
    end

    def weekly_date_range
      date = Time.zone.parse(filters[:due_date])
      start_date = date.beginning_of_week.utc
      end_date = (date.beginning_of_week + 4.days).end_of_day.utc
      [start_date, end_date]
    end

    def student_uniq_course_enrollment_ids
      Enrollment.students.selectable
                .group('enrollments.canvas_course_id')
                .select('min(canvas_id) as canvas_id, canvas_course_id')
                .where(canvas_user_id: user_id)
    end
  end
end
