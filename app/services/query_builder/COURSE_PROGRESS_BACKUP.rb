# frozen_string_literal: true

module QueryBuilder
  class StudentCourseProgress < BaseQueryBuilder
    def self.from_valid_shard(student, options = {})
      raise ArgumentError, 'student is required' if student.blank?
      raise ArgumentError, 'course_shard_id is required' if options[:course_shard_id].blank?

      course_shard_id = options[:course_shard_id].to_s
      course_progress_data = {}
      request_org = current_organization
      student.against_shards do |shard_student|
        next unless current_organization.canvas_shard_id.to_s == course_shard_id

        course_progress_data = StudentCourseProgress.new({
                                                           canvas_user_id: shard_student.canvas_id,
                                                           canvas_course_id: options[:canvas_course_id],
                                                           filters: options[:filters],
                                                           organization: current_organization,
                                                           request_org: request_org
                                                         }).records
      end
      course_progress_data
    end

    def records
      @user = User.find_by(canvas_id: user_id)
      @course = Course.find_by(canvas_id: course_id)
      course_details = course_details_json
      # Create celebration events for completed modules
      create_module_celebration_events(course_details[:modules])
      course_details.merge!(show_fireworks: pending_module_celebration_event?)
      course_details
    end

    def pending_module_celebration_event?
      CelebrationEvent.exists?(
        canvas_user_id: @user.canvas_id,
        canvas_course_id: @course.canvas_id,
        event_type: 'module_completed',
        shown_at: nil
      )
    end

    def course_details_json
      canvas_course_progress = []
      indexed_canvas_course_progress = {}
      Canvas.with_shard do
        canvas_course_progress = Canvas::Course.course_module_progress(course_id, user_id)

        indexed_canvas_course_progress = canvas_course_progress.index_by { |h| h['context_module_item_id'] }
      end

      course_progress_data = calculate_progress_from_canvas_data(canvas_course_progress)

      course_progress = Course.where(canvas_id: course_id).joins(sql_joins([course_id])).select(sql_selects).first

      {
        id: @course.id,
        canvas_id: @course.canvas_id,
        name: @course.name,
        course_code: @course.course_code,
        course_sis_id: @course.sis_id,
        current_score: course_progress&.current_score,
        current_letter_grade: course_progress&.current_letter_grade,
        requirement_count: course_progress_data[:requirement_count],
        requirement_completed_count: course_progress_data[:requirement_completed_count],
        course_progress_percent: calculate_course_progress_percent_from_data(course_progress_data),
        # Calculate expected progress from Canvas data
        expected_course_progress: calculate_expected_course_progress_from_data(course_progress_data),
        expected_course_progress_percent: calculate_expected_course_progress_percent_from_data(course_progress_data),
        color_code: fetch_color_code(course_progress&.current_score),
        modules: course_module_items_with_canvas_data(indexed_canvas_course_progress),
        teacher: @course.teachers.first&.name
      }
    end

    # Determines if a submission should be filtered out based on its status and the active filters
    # If a filter is in the filters array, we should filter out items of that type
    def filtered_out?(submission)
      return false if filters.blank?

      (filters.include?('master') && submission.master?) ||
        (filters.include?('not_master') && submission.not_master?) ||
        (filters.include?('not_completed') && submission.not_completed?) ||
        (filters.include?('not_completed_past_due') && submission.not_completed_past_due?)
    end

    # Determines if a module item should be filtered out based on its status and the active filters
    # Works for both assignment and non-assignment items
    def filtered_out_item?(submission, req_status)
      return false if filters.blank?

      # For assignment items, use the existing submission-based filtering
      return filtered_out?(submission) if submission

      # For non-assignment items, filter based on requirement status
      case req_status
      when 'mastered'
        filters.include?('master')
      when 'not mastered'
        filters.include?('not_master')
      when 'past due'
        filters.include?('not_completed_past_due')
      when 'not completed'
        filters.include?('not_completed')
      when 'completed'
        filters.include?('completed')
      else
        false
      end
    end

    # def build_item_json(item, progression, req_status, submission: nil, assignment: nil)
    #   course_sharded_id = course_sharded_id_string

    #   # Build common data structure
    #   base_data = {
    #     id: item.canvas_id,
    #     item_type: item.canvas_content_type,
    #     requirement_status: req_status,
    #     requirement_type: progression&.requirement_type,
    #     course_content_path: generate_content_path(item, progression),
    #     organization_shard_id: organization_shard_id,
    #     canvas_course_sharded_id: course_sharded_id
    #   }

    #   # Add assignment-specific or non-assignment data
    #   assignment_data = submission || assignment ? build_assignment_data(item, progression, submission, assignment) : build_non_assignment_data(item, progression)

    #   base_data.merge(assignment_data)
    # end

    # def build_assignment_data(item, progression, submission, assignment)
    #   calculated_score = (calculate_score_percent(submission.score, submission.points_possible) if submission.present?)

    #   {
    #     canvas_assignment_id: item.canvas_assignment_id,
    #     assignment_name: assignment&.title || progression&.canvas_content_title,
    #     assignment_due_at: assignment&.due_at,
    #     canvas_user_id: @user.canvas_id,
    #     due_date: submission&.due_at || progression&.due_at || assignment&.due_at,
    #     completed_date: submission&.submitted_at,
    #     graded_at: submission&.graded_at,
    #     points_possible: assignment&.points_possible,
    #     score: submission&.score,
    #     score_percent: calculated_score,
    #     letter_grade: fetch_letter_grade(calculated_score),
    #     color_code: fetch_color_code(calculated_score),
    #     submission_status: submission&.workflow_state
    #   }
    # end

    # def build_non_assignment_data(item, progression)
    #   {
    #     canvas_assignment_id: nil,
    #     assignment_name: progression&.canvas_content_title || item&.title,
    #     assignment_due_at: progression&.due_at,
    #     canvas_user_id: @user.canvas_id,
    #     due_date: progression&.due_at || progression&.todo_date || progression&.lock_at,
    #     completed_date: progression&.requirement_status == 'completed' ? progression&.updated_at : nil,
    #     graded_at: nil,
    #     points_possible: nil,
    #     score: nil,
    #     score_percent: nil,
    #     letter_grade: nil,
    #     color_code: nil,
    #     submission_status: nil
    #   }
    # end

    def course_sharded_id_string
      organization_shard_id ? course_sharded_id(@course.canvas_id).to_s : @course.canvas_id.to_s
    end

    # # Determines the requirement status based on progression and submission data
    # def determine_requirement_status(progression, submission)
    #   # For assignment items, use submission status if available
    #   return submission.completed_status if submission && progression&.requirement_status == 'completed'

    #   return submission.requirement_status if submission

    #   # For non-assignment items, map progression status to display status
    #   case progression&.requirement_status
    #   when 'completed'
    #     'completed'
    #   when 'incomplete'
    #     if past_due?(progression)
    #       'past due'
    #     else
    #       'not completed'
    #     end
    #   else
    #     'not completed'
    #   end
    # end

    # Generates the generic Canvas module item path for all content types with sharding support
    def generate_content_path(canvas_data)
      course_sharded_id = course_sharded_id_string
      "courses/#{course_sharded_id}/modules/items/#{canvas_data['context_module_item_id']}"
    end

    # def same_shard?
    #   request_org&.canvas_shard_id.to_s == organization_shard_id.to_s
    # end

    # # Checks if an item is past due based on progression dates
    # def past_due?(progression)
    #   due_date = progression&.todo_date || progression&.due_at || progression&.lock_at
    #   due_date && due_date < Time.current
    # end

    private

    def calculate_progress_from_canvas_data(canvas_course_progress)
      return { requirement_count: 0, requirement_completed_count: 0, past_due_requirements_count: 0, requirements_with_due_date_count: 0 } if canvas_course_progress.empty?

      requirement_count = canvas_course_progress.length
      requirement_completed_count = canvas_course_progress.count { |item| item['module_progress_status'] == 'completed' }

      # Calculate past due count
      past_due_requirements_count = canvas_course_progress.count do |item|
        next false if item['module_progress_status'] == 'completed'

        due_date = item['todo_date'] || item['lock_at']
        due_date && due_date.to_time < Time.current
      rescue ArgumentError
        false
      end

      # Calculate requirements with due date count
      requirements_with_due_date_count = canvas_course_progress.count do |item|
        item['todo_date'].present? || item['lock_at'].present?
      end

      {
        requirement_count: requirement_count,
        requirement_completed_count: requirement_completed_count,
        past_due_requirements_count: past_due_requirements_count,
        requirements_with_due_date_count: requirements_with_due_date_count
      }
    end

    def course_module_items_with_canvas_data(indexed_canvas_course_progress)
      modules = fetch_active_modules
      preloaded_data = preload_module_data(modules)

      build_module_data_with_canvas(modules, preloaded_data, indexed_canvas_course_progress)
    end

    def build_module_data_with_canvas(modules, preloaded_data, indexed_canvas_course_progress)
      modules.map do |context_module|
        items = context_module.context_module_items.filter_map do |item|
          module_item_progress = indexed_canvas_course_progress[item.canvas_id] || {}
          item_data = build_item_data_with_canvas(item, preloaded_data, module_item_progress)
          item_data
        end

        # Calculate module status counts
        status_counts = calculate_module_status_counts(items)

        {
          module_id: context_module.canvas_id,
          name: context_module.name,
          module_progress_status: determine_module_progress_status(items),
          items: items
        }.merge(status_counts)
      end
    end

    def build_item_data_with_canvas(item, preloaded_data, module_item_progress)
      submission = preloaded_data[:submissions][item.canvas_assignment_id]
      assignment = preloaded_data[:assignments][item.canvas_assignment_id]

      # Determine requirement status from Canvas data
      requirement_status = determine_requirement_status_from_canvas(module_item_progress, submission, assignment)

      return if filtered_out_item?(submission, requirement_status)

      # binding.pry if item.canvas_id == 390
      pp submission if item.canvas_id == 390
      # Parse Canvas dates
      due_date = parse_canvas_date(module_item_progress['todo_date']) || parse_canvas_date(module_item_progress['lock_at']) || parse_canvas_date(module_item_progress['due_date'])
      completed_date = parse_canvas_date(module_item_progress['completed_date'])
      assignment_due_at = assignment&.due_at

      # Calculate scores and grades
      score_percent = calculate_score_percent(module_item_progress['score'], module_item_progress['points_possible'])
      letter_grade = fetch_letter_grade(module_item_progress['score'])
      color_code = fetch_color_code(score_percent)

      {
        id: item.canvas_id,
        item_type: item.canvas_content_type,
        requirement_status: requirement_status,
        requirement_type: module_item_progress['requirement_type'],
        course_content_path: generate_content_path(module_item_progress),
        organization_shard_id: :organization_shard_id,
        canvas_course_sharded_id: course_sharded_id_string,
        canvas_assignment_id: item.canvas_assignment_id,
        assignment_name: assignment&.title || item.title,
        assignment_due_at: assignment_due_at,
        canvas_user_id: @user.canvas_id.to_s,
        due_date: due_date,
        completed_date: completed_date,
        graded_at: module_item_progress['graded_at'],
        points_possible: module_item_progress['points_possible'],
        score: module_item_progress['score'],
        score_percent: score_percent,
        letter_grade: letter_grade,
        color_code: color_code,
        submission_status: module_item_progress['submission_status']
      }
    end

    # def past_due_from_canvas_data?(canvas_data)
    #   return false if canvas_data['module_progress_status'] == 'completed'

    #   due_date = canvas_data['todo_date'] || canvas_data['lock_at']
    #   return false unless due_date

    #   Time.zone.parse(due_date) < Time.current
    # rescue ArgumentError
    #   false
    # end

    def parse_canvas_date(date_value)
      return nil unless date_value.present?
      return date_value if date_value.is_a?(Time)

      Time.zone.parse(date_value.to_s)
    rescue ArgumentError
      nil
    end

    def determine_requirement_status_from_canvas(module_item_progress, submission, _assignment)
      # Map Canvas module progress status to requirement status

      # WHEN context_module_items.canvas_assignment_id IS NOT NULL AND submissions.workflow_state IN ('graded', 'submitted') AND COALESCE((submissions.score / NULLIF(submissions.points_possible,0))*100,0) > 80 THEN 'mastered'
      # WHEN context_module_items.canvas_assignment_id IS NOT NULL AND submissions.workflow_state IN ('graded', 'submitted') AND COALESCE((submissions.score / NULLIF(submissions.points_possible,0))*100,0) <= 80 THEN 'not mastered'
      # WHEN context_module_items.canvas_assignment_id IS NOT NULL AND submissions.workflow_state IN ('graded', 'submitted') THEN 'not mastered'
      # WHEN context_module_items.canvas_assignment_id IS NOT NULL AND submissions.workflow_state IN ('submitted') THEN 'submitted'
      # WHEN context_module_items.canvas_assignment_id IS NOT NULL AND submissions.workflow_state IS NOT NULL THEN 'not started'
      # WHEN context_module_items.canvas_assignment_id IS NULL AND context_module_progressions.requirement_status = 'completed' THEN 'mastered'
      # WHEN context_module_items.canvas_assignment_id IS NULL AND context_module_progressions.requirement_status = 'incomplete' AND COALESCE(context_module_progressions.todo_date, context_module_progressions.due_at, context_module_progressions.lock_at) < NOW() THEN 'past due'
      # WHEN context_module_items.canvas_assignment_id IS NULL AND context_module_progressions.requirement_status = 'incomplete' THEN 'not completed'
      # WHEN context_module_items.canvas_assignment_id IS NULL AND context_module_progressions.requirement_status IS NULL THEN 'not started'

      ##################################################################################################################################
      # For assignment items, use submission status if available
      # binding.pry
      # return submission.completed_status if submission && progression&.requirement_status == 'completed'

      # return submission.requirement_status if submission

      # # For non-assignment items, map progression status to display status
      # case progression&.requirement_status
      # when 'completed'
      #   'completed'
      # when 'incomplete'
      #   if past_due?(progression)
      #     'past due'
      #   else
      #     'not completed'
      #   end
      # else
      #   'not completed'
      # end
      ##################################################################################################################################################################
      req_status = requirement_status(module_item_progress)
      return submission.completed_status if submission && req_status == 'completed'

      return submission.requirement_status if submission

      case req_status
      when 'completed'
        'completed'
      when 'incomplete'
        if past_due_from_canvas_data?(module_item_progress)
          'past due'
        else
          'not completed'
        end
      else
        'not completed'
      end

      ##################################################################################################################################################################
      # if submission
      #   if submission.workflow_state == 'graded' || submission.workflow_state == 'submitted'
      #     score_percent = calculate_score_percent(module_item_progress['score'], module_item_progress['points_possible'])
      #     score_percent >= 80 ? 'mastered' : 'not mastered'
      #   elsif submission.workflow_state == 'submitted'
      #     'submitted'
      #   elsif submission.due_at && submission.due_at < Time.current
      #     'past due'
      #   else
      #     'not started'
      #   end
      # else
      #   if req_status == 'completed'
      #     req_status
      #   elsif (module_item_progress['todo_date'] || module_item_progress['due_at']) && (module_item_progress['todo_date'] || module_item_progress['due_at']).to_time < Time.current
      #     'past due'
      #   else
      #     'not completed'
      #   end
      # end

      # case module_item_progress['module_progress_status']
      # when 'completed'
      #   if submission && assignment
      #     # For assignments, determine mastery level
      #     score_percent = calculate_score_percent(module_item_progress['score'], module_item_progress['points_possible'])
      #     score_percent >= 80 ? 'mastered' : 'not mastered'
      #   else
      #     'completed'
      #   end
      # when 'started', 'in_progress'
      #   'not completed'
      # else
      #   'not completed'
      # end
    end

    def past_due_from_canvas_data?(module_item_progress)
      (module_item_progress['todo_date'] || module_item_progress['due_at']) && (module_item_progress['todo_date'] || module_item_progress['due_at']).to_time < Time.current
    end

    def completed_requirement(row)
      return [] if row['requirements_met'].nil?

      prog_req = YAML.safe_load(row['requirements_met'], permitted_classes: [Symbol])
      prog_req.select { |req| req[:id].to_s == row['context_module_item_id'].to_s }
    end

    def requirement_status(row)
      req_completed = completed_requirement(row)
      req_completed.any? ? 'completed' : 'incomplete'
    end

    def calculate_module_status_counts(items)
      counts = items.group_by { |item| item[:requirement_status] }
                    .transform_values(&:count)
      {
        'items_count' => items.length,
        'mastered' => counts['mastered'] || 0,
        'completed' => counts['completed'] || 0,
        'not mastered' => counts['not mastered'] || 0,
        'not completed' => counts['not completed'] || 0,
        'past due' => counts['past due'] || 0
      }
    end

    def determine_module_progress_status(items)
      return 'locked' if items.empty?

      completed_count = items.count { |item| %w[completed mastered].include?(item[:requirement_status]) }

      if completed_count == items.length
        'completed'
      elsif completed_count.positive?
        'started'
      else
        'unlocked'
      end
    end

    def fetch_active_modules
      @course.context_modules
             .includes(:context_module_items)
             .where(workflow_state: 'active')
             .where(context_module_items: { canvas_id: @course.module_requiment_ids, workflow_state: 'active' })
             .order('context_modules.position ASC, context_module_items.position ASC')
    end

    def preload_module_data(modules)
      assignment_ids = modules.flat_map { |mod| mod.context_module_items.map(&:canvas_assignment_id).compact }

      {
        submissions: preload_submissions(assignment_ids),
        assignments: preload_assignments(assignment_ids)
      }
    end

    def preload_submissions(assignment_ids)
      Submission.where(
        canvas_assignment_id: assignment_ids,
        canvas_user_id: @user.canvas_id
      ).index_by(&:canvas_assignment_id)
    end

    def preload_assignments(assignment_ids)
      Assignment.where(canvas_id: assignment_ids).index_by(&:canvas_id)
    end

    # def build_module_data(modules, preloaded_data)
    #   data = []
    #   modules.each do |mod|
    #     module_record = process_module(mod, preloaded_data)
    #     data << module_record if module_record[:items_count].positive?
    #   end
    #   data
    # end

    # def process_module(mod, preloaded_data)
    #   rec = initialize_module_record(mod)

    #   mod.context_module_items.each do |item|
    #     process_module_item(item, rec, preloaded_data)
    #   end

    #   rec
    # end

    # def initialize_module_record(mod)
    #   cmp = mod.context_module_progressions
    #   module_progress_status = cmp.map(&:module_progress_status).uniq.first || nil
    #   {
    #     module_id: mod.canvas_id,
    #     name: mod.name,
    #     module_progress_status: module_progress_status,
    #     items_count: 0,
    #     items: []
    #   }
    # end

    # def process_module_item(item, rec, preloaded_data)
    #   progression = preloaded_data[:progressions][item.canvas_id]
    #   # return unless progression

    #   submission, assignment = get_submission_and_assignment(item, preloaded_data)
    #   req_status = determine_requirement_status(progression, submission)

    #   return if filtered_out_item?(submission, req_status)

    #   item_data = {
    #     item: item,
    #     progression: progression,
    #     submission: submission,
    #     assignment: assignment
    #   }
    #   update_module_record(rec, req_status, item_data)
    # end

    # def get_submission_and_assignment(item, preloaded_data)
    #   return [nil, nil] unless item.canvas_assignment_id.present?

    #   submission = preloaded_data[:submissions][item.canvas_assignment_id]
    #   assignment = preloaded_data[:assignments][item.canvas_assignment_id]
    #   [submission, assignment]
    # end

    # def update_module_record(rec, req_status, item_data)
    #   rec[req_status] = rec[req_status].to_i + 1
    #   rec[:items_count] += 1
    #   rec[:items] << build_item_json(
    #     item_data[:item],
    #     item_data[:progression],
    #     req_status,
    #     submission: item_data[:submission],
    #     assignment: item_data[:assignment]
    #   )
    # end

    def sql_joins(_canvas_course_ids)
      <<~SQL.squish
        LEFT OUTER JOIN #{Enrollment.quoted_table_name} e
          ON e.canvas_user_id = #{user_id}
          AND e.canvas_course_id = courses.canvas_id
          AND e.base_role_type = 'StudentEnrollment' AND (e.workflow_state <> 'deleted')
        LEFT OUTER JOIN #{Score.quoted_table_name} scores ON scores.canvas_enrollment_id = e.canvas_id
      SQL
    end

    def sql_selects
      <<~SQL.squish
        courses.canvas_id AS canvas_course_id,
        e.canvas_id AS canvas_enrollment_id,
        scores.current_score AS current_score,
        scores.current_letter_grade AS current_letter_grade
      SQL
    end

    # def calculate_course_progress_percent(course_progress)
    #   return 0 unless course_progress&.requirement_count&.positive?

    #   ((course_progress.requirement_completed_count.to_f / course_progress.requirement_count) * 100).round(0)
    # end

    def calculate_course_progress_percent_from_data(course_progress_data)
      return 0 unless course_progress_data[:requirement_count]&.positive?

      ((course_progress_data[:requirement_completed_count].to_f / course_progress_data[:requirement_count]) * 100).round(0)
    end

    # Expected Course Progress = number of items that should be completed by now (items with past due dates + current items)
    # This represents what percentage of the course should be completed based on due dates
    def calculate_expected_course_progress_from_data(course_progress_data)
      return 0 unless course_progress_data[:requirements_with_due_date_count]&.positive?

      # Expected progress should be 100% if all items with due dates are past due
      # This means the student should have completed all items with due dates by now
      past_due_items = course_progress_data[:past_due_requirements_count]

      # If there are past due items, expected progress should be 100%
      # (all items with due dates should be completed)
      return 100 if past_due_items.positive?

      # If no items are past due yet, expected progress is based on current timeline
      # For now, return 100% as the baseline expectation
      100
    end

    # Percentage of Expected Course Progress = percentage of items completed vs Expected Course Progress
    def calculate_expected_course_progress_percent_from_data(course_progress_data)
      expected_progress = calculate_expected_course_progress_from_data(course_progress_data)
      return 0 if expected_progress.zero?

      actual_progress = calculate_course_progress_percent_from_data(course_progress_data)
      (actual_progress.to_f / expected_progress * 100).round(0)
    end

    def calculate_score_percent(score, points_possible)
      return 0.0 if points_possible.to_f <= 0

      ((score.to_f / points_possible) * 100).round(2)
    end

    def fetch_letter_grade(score)
      return nil if score.nil? || grading_scheme.nil?

      grading_scheme&.score_to_grade(score)&.dig('name')
    end

    def fetch_color_code(score)
      return nil if score.nil? || grading_scheme_colors.nil?

      grading_scheme_color = grading_scheme_colors&.by_score(score)
      grading_scheme_color&.color_code || grading_scheme_color&.default_color_code
    end

    def grading_scheme
      @grading_scheme ||= @course&.grading_scheme
    end

    def grading_scheme_colors
      @grading_scheme_colors ||= grading_scheme&.grading_scheme_colors
    end

    def create_module_celebration_events(modules)
      modules.each do |mod|
        celebration_event = CelebrationEvent.find_by(
          canvas_user_id: @user.canvas_id,
          canvas_course_id: @course.canvas_id,
          event_type: 'module_completed',
          canvas_module_id: mod[:module_id]
        )

        if mod[:module_progress_status] != 'completed' && celebration_event.present?
          # If the module is no longer completed, remove any existing celebration event
          celebration_event.destroy
        end

        next unless mod[:module_progress_status] == 'completed'

        CelebrationEvent.find_or_create_by!(
          canvas_user_id: @user.canvas_id,
          canvas_course_id: @course.canvas_id,
          event_type: 'module_completed',
          canvas_module_id: mod[:module_id]
        )
      end
    rescue StandardError => e
      Rails.logger.error "Error creating module celebration events: #{e.message}"
      Rails.logger.error e.backtrace.join("\n")
      raise e
    end
  end
end
