2 AND "enrollments"."workflow_state" = $3 ORDER BY "users"."id" ASC LIMIT $4  [["canvas_course_id", 4], ["base_role_type", "TeacherEnrollment"], ["workflow_state", "active"], ["LIMIT", 1]]
  ↳ app/services/query_builder/student_course_progress.rb:65:in `course_details_json'

From: /Users/<USER>/Documents/work/mukesh/projects/tech9/stride-k5-dashboard/app/services/query_builder/student_course_progress.rb:30 QueryBuilder::StudentCourseProgress#records:

    26: def records
    27:   @user = User.find_by(canvas_id: user_id)
    28:   @course = Course.find_by(canvas_id: course_id)
    29:   course_details = course_details_json
 => 30:   binding.pry
    31:   # Create celebration events for completed modules
    32:   create_module_celebration_events(course_details[:modules])
    33:   course_details.merge!(show_fireworks: pending_module_celebration_event?)
    34:   course_details
    35: end

StrideK5Da-dev-default:local[1] pry(#<QueryBuilder::StudentCourseProgress>)>
StrideK5Da-dev-default:local[2] pry(#<QueryBuilder::StudentCourseProgress>)> canvas_course_progress
  AccountGradingSchemeColor Load (0.3ms)  SELECT "account_grading_scheme_colors".* FROM "account_grading_scheme_colors" WHERE "account_grading_scheme_colors"."canvas_account_id" = $1 AND "account_grading_scheme_colors"."scheme_color_type" = $2 /* loading for inspect */ ORDER BY "account_grading_scheme_colors"."id" ASC LIMIT $3  [["canvas_account_id", 2], ["scheme_color_type", 0], ["LIMIT", 11]]
  ↳ app/services/query_builder/student_course_progress.rb:30:in `records'
  CACHE AccountGradingSchemeColor Load (0.0ms)  SELECT "account_grading_scheme_colors".* FROM "account_grading_scheme_colors" WHERE "account_grading_scheme_colors"."canvas_account_id" = $1 AND "account_grading_scheme_colors"."scheme_color_type" = $2 /* loading for inspect */ ORDER BY "account_grading_scheme_colors"."id" ASC LIMIT $3  [["canvas_account_id", 2], ["scheme_color_type", 0], ["LIMIT", 11]]
  ↳ app/services/query_builder/student_course_progress.rb:30:in `records'
NameError: undefined local variable or method `canvas_course_progress' for #<QueryBuilder::StudentCourseProgress:0x000000011cd8ffc8 @user_id=17, @course_id=4, @filters={}, @organization_id=1, @organization_name="local", @organization_base_url="http://canvas.docker", @organization_shard_id=1, @request_org=#<PandaPal::Organization id: 1, name: "local", key: "**************", secret: "faKHXKYBLQ8rHA7zxAvAXf8cGZvY7uVc4HaxuDuXBrvRTBf6BN...", canvas_account_id: "2", created_at: "2025-09-11 16:06:43.********* +0530", updated_at: "2025-09-15 19:41:34.********* +0530", salesforce_id: "0", encrypted_settings: "7Ur2jW1ZqRH6yqqTEaUUF8tBfO5aeZhJcHHBzdhDeS7b3SVbwT...", encrypted_settings_iv: "jn1xSwBl1pZNGjAz\n", shard: "default", canvas_shard_id: 1, settings: nil>, @user=#<User id: 13, canvas_id: 17, email: "<EMAIL>", first_name: "k5 Student", last_name: "1", workflow_state: "active", login_id: "<EMAIL>", name: "k5 Student 1", sortable_name: "1, k5 Student", created_at: "2025-09-11 16:16:18.********* +0530", updated_at: "2025-09-11 16:16:18.********* +0530", canvas_role_id: nil, grade_level: "5">, @course=#<Course id: 5, canvas_id: 4, sis_id: nil, course_code: "K5 Dashboard Test", name: "K5 Dashboard Test", workflow_state: "active", canvas_account_id: 2, canvas_term_id: 2, start_at: nil, end_at: nil, grading_standard_id: 0, created_at: "2025-09-11 16:16:18.********* +0530", updated_at: "2025-09-12 22:32:05.********* +0530", image_url: "http://canvas.docker/courses/4/files/144/download?...", grade_level: nil>, @grading_scheme=#<GradingScheme id: 3, canvas_course_id: 4, data: [{"name"=>"A", "value"=>0.94}, {"name"=>"A-", "value"=>0.9}, {"name"=>"B+", "value"=>0.87}, {"name"=>"B", "value"=>0.84}, {"name"=>"B-", "value"=>0.8}, {"name"=>"C+", "value"=>0.77}, {"name"=>"C", "value"=>0.74}, {"name"=>"C-", "value"=>0.7}, {"name"=>"D+", "value"=>0.67}, {"name"=>"D", "value"=>0.64}, {"name"=>"D-", "value"=>0.61}, {"name"=>"F", "value"=>0.0}], scheme_color_type: "standard", created_at: "2025-09-11 16:16:40.********* +0530", updated_at: "2025-09-11 16:16:40.********* +0530">, @grading_scheme_colors=#<ActiveRecord::AssociationRelation [#<AccountGradingSchemeColor id: 1, canvas_account_id: 2, scheme_color_type: "standard", name: "A", range_value: "0.94", color_code: "#4FD7E0", default_color_code: "#027A48", created_at: "2025-09-11 16:16:40.********* +0530", updated_at: "2025-09-11 17:37:20.********* +0530">, #<AccountGradingSchemeColor id: 2, canvas_account_id: 2, scheme_color_type: "standard", name: "A-", range_value: "0.9", color_code: "#ABD47B", default_color_code: "#027A48", created_at: "2025-09-11 16:16:40.********* +0530", updated_at: "2025-09-11 17:37:29.********* +0530">, #<AccountGradingSchemeColor id: 3, canvas_account_id: 2, scheme_color_type: "standard", name: "B+", range_value: "0.87", color_code: nil, default_color_code: "#387C0F", created_at: "2025-09-11 16:16:40.********* +0530", updated_at: "2025-09-11 16:16:40.********* +0530">, #<AccountGradingSchemeColor id: 4, canvas_account_id: 2, scheme_color_type: "standard", name: "B", range_value: "0.84", color_code: nil, default_color_code: "#387C0F", created_at: "2025-09-11 16:16:40.********* +0530", updated_at: "2025-09-11 16:16:40.********* +0530">, #<AccountGradingSchemeColor id: 5, canvas_account_id: 2, scheme_color_type: "standard", name: "B-", range_value: "0.8", color_code: nil, default_color_code: "#387C0F", created_at: "2025-09-11 16:16:40.********* +0530", updated_at: "2025-09-11 16:16:40.********* +0530">, #<AccountGradingSchemeColor id: 6, canvas_account_id: 2, scheme_color_type: "standard", name: "C+", range_value: "0.77", color_code: nil, default_color_code: "#B54708", created_at: "2025-09-11 16:16:40.********* +0530", updated_at: "2025-09-11 16:16:40.********* +0530">, #<AccountGradingSchemeColor id: 7, canvas_account_id: 2, scheme_color_type: "standard", name: "C", range_value: "0.74", color_code: nil, default_color_code: "#B54708", created_at: "2025-09-11 16:16:40.********* +0530", updated_at: "2025-09-11 16:16:40.********* +0530">, #<AccountGradingSchemeColor id: 8, canvas_account_id: 2, scheme_color_type: "standard", name: "C-", range_value: "0.7", color_code: nil, default_color_code: "#B54708", created_at: "2025-09-11 16:16:40.********* +0530", updated_at: "2025-09-11 16:16:40.********* +0530">, #<AccountGradingSchemeColor id: 9, canvas_account_id: 2, scheme_color_type: "standard", name: "D+", range_value: "0.67", color_code: nil, default_color_code: "#B93815", created_at: "2025-09-11 16:16:40.********* +0530", updated_at: "2025-09-11 16:16:40.********* +0530">, #<AccountGradingSchemeColor id: 10, canvas_account_id: 2, scheme_color_type: "standard", name: "D", range_value: "0.64", color_code: nil, default_color_code: "#B93815", created_at: "2025-09-11 16:16:40.********* +0530", updated_at: "2025-09-11 16:16:40.********* +0530">, ...]>>

canvas_course_progress
^^^^^^^^^^^^^^^^^^^^^^
from (pry):24:in `records'
StrideK5Da-dev-default:local[3] pry(#<QueryBuilder::StudentCourseProgress>)>
StrideK5Da-dev-default:local[4] pry(#<QueryBuilder::StudentCourseProgress>)>
StrideK5Da-dev-default:local[5] pry(#<QueryBuilder::StudentCourseProgress>)>
StrideK5Da-dev-default:local[6] pry(#<QueryBuilder::StudentCourseProgress>)> [200~course_details~
SyntaxError: unexpected '~', expecting ']'
[200~course_details~
    ^
StrideK5Da-dev-default:local[6] pry(#<QueryBuilder::StudentCourseProgress>)> course_details
=>
{:id=>5,
 :canvas_id=>4,
 :name=>"K5 Dashboard Test",
 :course_code=>"K5 Dashboard Test",
 :course_sis_id=>nil,
 :current_score=>90.0,
 :current_letter_grade=>"A-",
 :requirement_count=>14,
 :requirement_completed_count=>12,
 :past_due_requirements_count=>3,
 :requirements_with_due_date_count=>3,
 :course_progress_percent=>86,
 :expected_course_progress=>100,
 :expected_course_progress_percent=>86,
 :color_code=>"#ABD47B",
 :modules=>
  [{:module_id=>21,
    :name=>"2: After again  Unit2-CDN tests",
    :module_progress_status=>"completed",
    :items_count=>12,
    :items=>
     [{:id=>388,
       :item_type=>"Assignment",
       :requirement_status=>"mastered",
       :requirement_type=>"must_submit",
       :course_content_path=>"courses/10000000000004/modules/items/388",
       :organization_shard_id=>1,
       :canvas_course_sharded_id=>"10000000000004",
       :canvas_assignment_id=>104,
       :assignment_name=>"2.01 Interim Checkpoint: CDN Tests-standalone-HKSDELL33452",
       :assignment_due_at=>Fri, 12 Sep 2025 11:29:00.000000000 IST +05:30,
       :canvas_user_id=>"17",
       :due_date=>Fri, 12 Sep 2025 11:29:00.000000000 IST +05:30,
       :completed_date=>Fri, 12 Sep 2025 13:38:51.000000000 IST +05:30,
       :graded_at=>Fri, 12 Sep 2025 13:40:23.000000000 IST +05:30,
       :points_possible=>2.0,
       :score=>1.8,
       :score_percent=>90.0,
       :letter_grade=>"A-",
       :color_code=>"#ABD47B",
       :submission_status=>"graded"},
      {:id=>389,
       :item_type=>"ContextExternalTool",
       :requirement_status=>"completed",
       :requirement_type=>"must_view",
       :course_content_path=>"courses/10000000000004/modules/items/389",
       :organization_shard_id=>1,
       :canvas_course_sharded_id=>"10000000000004",
       :canvas_assignment_id=>nil,
       :assignment_name=>"60-Second Math: New Activity-CDN",
       :assignment_due_at=>nil,
       :canvas_user_id=>"17",
       :due_date=>nil,
       :completed_date=>Fri, 12 Sep 2025 15:01:28.668684000 IST +05:30,
       :graded_at=>nil,
       :points_possible=>nil,
       :score=>nil,
       :score_percent=>nil,
       :letter_grade=>nil,
       :color_code=>nil,
       :submission_status=>nil},
      {:id=>390,
       :item_type=>"Assignment",
       :requirement_status=>"not mastered",
       :requirement_type=>"must_view",
       :course_content_path=>"courses/10000000000004/modules/items/390",
       :organization_shard_id=>1,
       :canvas_course_sharded_id=>"10000000000004",
       :canvas_assignment_id=>105,
       :assignment_name=>"2.01 Interim Checkpoint: SQA ISSUE 05212021",
       :assignment_due_at=>nil,
       :canvas_user_id=>"17",
       :due_date=>nil,
       :completed_date=>nil,
       :graded_at=>nil,
       :points_possible=>2.0,
       :score=>nil,
       :score_percent=>0.0,
       :letter_grade=>"F",
       :color_code=>"#B42318",
       :submission_status=>"unsubmitted"},
      {:id=>391,
       :item_type=>"ContextExternalTool",
       :requirement_status=>"completed",
       :requirement_type=>"must_view",
       :course_content_path=>"courses/10000000000004/modules/items/391",
       :organization_shard_id=>1,
       :canvas_course_sharded_id=>"10000000000004",
       :canvas_assignment_id=>nil,
       :assignment_name=>"60-Second Math: SQA - Act added after DP-24200 New Activity-CDN",
       :assignment_due_at=>nil,
       :canvas_user_id=>"17",
       :due_date=>nil,
       :completed_date=>Fri, 12 Sep 2025 15:01:28.668684000 IST +05:30,
       :graded_at=>nil,
       :points_possible=>nil,
       :score=>nil,
       :score_percent=>nil,
       :letter_grade=>nil,
       :color_code=>nil,
       :submission_status=>nil},
      {:id=>392,
       :item_type=>"ContextExternalTool",
       :requirement_status=>"completed",
       :requirement_type=>"must_view",
       :course_content_path=>"courses/10000000000004/modules/items/392",
       :organization_shard_id=>1,
       :canvas_course_sharded_id=>"10000000000004",
       :canvas_assignment_id=>nil,
       :assignment_name=>"60-Second Math: SQA - Act added after second bounce DP-24200 New Activity-CDN",
       :assignment_due_at=>nil,
       :canvas_user_id=>"17",
       :due_date=>nil,
       :completed_date=>Fri, 12 Sep 2025 15:01:28.668684000 IST +05:30,
       :graded_at=>nil,
       :points_possible=>nil,
       :score=>nil,
       :score_percent=>nil,
       :letter_grade=>nil,
       :color_code=>nil,
       :submission_status=>nil},
      {:id=>393,
       :item_type=>"ContextExternalTool",
       :requirement_status=>"completed",
       :requirement_type=>"must_view",
       :course_content_path=>"courses/10000000000004/modules/items/393",
       :organization_shard_id=>1,
       :canvas_course_sharded_id=>"10000000000004",
       :canvas_assignment_id=>nil,
       :assignment_name=>"60-Second Math: SQA - Act added Oct 10th - DP-24200",
       :assignment_due_at=>nil,
       :canvas_user_id=>"17",
       :due_date=>nil,
       :completed_date=>Fri, 12 Sep 2025 15:01:28.668684000 IST +05:30,
       :graded_at=>nil,
       :points_possible=>nil,
       :score=>nil,
       :score_percent=>nil,
       :letter_grade=>nil,
       :color_code=>nil,
       :submission_status=>nil},
      {:id=>585,
       :item_type=>"WikiPage",
       :requirement_status=>"completed",
       :requirement_type=>"must_view",
       :course_content_path=>"courses/10000000000004/modules/items/585",
       :organization_shard_id=>1,
       :canvas_course_sharded_id=>"10000000000004",
       :canvas_assignment_id=>nil,
       :assignment_name=>"Module 1",
       :assignment_due_at=>nil,
       :canvas_user_id=>"17",
       :due_date=>Fri, 12 Sep 2025 11:29:59.000000000 IST +05:30,
       :completed_date=>Fri, 12 Sep 2025 15:01:28.668684000 IST +05:30,
       :graded_at=>nil,
       :points_possible=>nil,
       :score=>nil,
       :score_percent=>nil,
       :letter_grade=>nil,
       :color_code=>nil,
       :submission_status=>nil},
      {:id=>586,
       :item_type=>"WikiPage",
       :requirement_status=>"completed",
       :requirement_type=>"must_mark_done",
       :course_content_path=>"courses/10000000000004/modules/items/586",
       :organization_shard_id=>1,
       :canvas_course_sharded_id=>"10000000000004",
       :canvas_assignment_id=>nil,
       :assignment_name=>"Module 1 Copy",
       :assignment_due_at=>nil,
       :canvas_user_id=>"17",
       :due_date=>Fri, 12 Sep 2025 11:29:59.000000000 IST +05:30,
       :completed_date=>Fri, 12 Sep 2025 15:01:28.668684000 IST +05:30,
       :graded_at=>nil,
       :points_possible=>nil,
       :score=>nil,
       :score_percent=>nil,
       :letter_grade=>nil,
       :color_code=>nil,
       :submission_status=>nil},
      {:id=>587,
       :item_type=>"ContextExternalTool",
       :requirement_status=>"completed",
       :requirement_type=>"must_view",
       :course_content_path=>"courses/10000000000004/modules/items/587",
       :organization_shard_id=>1,
       :canvas_course_sharded_id=>"10000000000004",
       :canvas_assignment_id=>nil,
       :assignment_name=>"EXT 1",
       :assignment_due_at=>nil,
       :canvas_user_id=>"17",
       :due_date=>nil,
       :completed_date=>Fri, 12 Sep 2025 15:01:28.668684000 IST +05:30,
       :graded_at=>nil,
       :points_possible=>nil,
       :score=>nil,
       :score_percent=>nil,
       :letter_grade=>nil,
       :color_code=>nil,
       :submission_status=>nil},
      {:id=>588,
       :item_type=>"ExternalUrl",
       :requirement_status=>"completed",
       :requirement_type=>"must_view",
       :course_content_path=>"courses/10000000000004/modules/items/588",
       :organization_shard_id=>1,
       :canvas_course_sharded_id=>"10000000000004",
       :canvas_assignment_id=>nil,
       :assignment_name=>"EXT URL 1",
       :assignment_due_at=>nil,
       :canvas_user_id=>"17",
       :due_date=>nil,
       :completed_date=>Fri, 12 Sep 2025 15:01:28.668684000 IST +05:30,
       :graded_at=>nil,
       :points_possible=>nil,
       :score=>nil,
       :score_percent=>nil,
       :letter_grade=>nil,
       :color_code=>nil,
       :submission_status=>nil},
      {:id=>589,
       :item_type=>"Quizzes::Quiz",
       :requirement_status=>"not mastered",
       :requirement_type=>"must_submit",
       :course_content_path=>"courses/10000000000004/modules/items/589",
       :organization_shard_id=>1,
       :canvas_course_sharded_id=>"10000000000004",
       :canvas_assignment_id=>151,
       :assignment_name=>"k5Course-Quiz-1",
       :assignment_due_at=>Fri, 12 Sep 2025 11:29:00.000000000 IST +05:30,
       :canvas_user_id=>"17",
       :due_date=>Fri, 12 Sep 2025 11:29:00.000000000 IST +05:30,
       :completed_date=>Fri, 12 Sep 2025 14:38:18.000000000 IST +05:30,
       :graded_at=>Fri, 12 Sep 2025 14:38:18.000000000 IST +05:30,
       :points_possible=>0.0,
       :score=>0.0,
       :score_percent=>0.0,
       :letter_grade=>"F",
       :color_code=>"#B42318",
       :submission_status=>"graded"},
      {:id=>590,
       :item_type=>"DiscussionTopic",
       :requirement_status=>"completed",
       :requirement_type=>"must_view",
       :course_content_path=>"courses/10000000000004/modules/items/590",
       :organization_shard_id=>1,
       :canvas_course_sharded_id=>"10000000000004",
       :canvas_assignment_id=>nil,
       :assignment_name=>"K5 Discussion - 1",
       :assignment_due_at=>nil,
       :canvas_user_id=>"17",
       :due_date=>Fri, 12 Sep 2025 11:29:59.000000000 IST +05:30,
       :completed_date=>Fri, 12 Sep 2025 15:01:28.668684000 IST +05:30,
       :graded_at=>nil,
       :points_possible=>nil,
       :score=>nil,
       :score_percent=>nil,
       :letter_grade=>nil,
       :color_code=>nil,
       :submission_status=>nil}],
    "mastered"=>1,
    "completed"=>9,
    "not mastered"=>2},
   {:module_id=>23,
    :name=>"4: Unit-SSO Verifications to BU",
    :module_progress_status=>"unlocked",
    :items_count=>2,
    :items=>
     [{:id=>432,
       :item_type=>"ContextExternalTool",
       :requirement_status=>"not completed",
       :requirement_type=>"must_view",
       :course_content_path=>"courses/10000000000004/modules/items/432",
       :organization_shard_id=>1,
       :canvas_course_sharded_id=>"10000000000004",
       :canvas_assignment_id=>nil,
       :assignment_name=>"60-Second Math: Standalone Activity with BU Library Link",
       :assignment_due_at=>nil,
       :canvas_user_id=>"17",
       :due_date=>nil,
       :completed_date=>nil,
       :graded_at=>nil,
       :points_possible=>nil,
       :score=>nil,
       :score_percent=>nil,
       :letter_grade=>nil,
       :color_code=>nil,
       :submission_status=>nil},
      {:id=>433,
       :item_type=>"ContextExternalTool",
       :requirement_status=>"not completed",
       :requirement_type=>"must_view",
       :course_content_path=>"courses/10000000000004/modules/items/433",
       :organization_shard_id=>1,
       :canvas_course_sharded_id=>"10000000000004",
       :canvas_assignment_id=>nil,
       :assignment_name=>"60-Second Math: Standalone Book Activity with BU Book",
       :assignment_due_at=>nil,
       :canvas_user_id=>"17",
       :due_date=>nil,
       :completed_date=>nil,
       :graded_at=>nil,
       :points_possible=>nil,
       :score=>nil,
       :score_percent=>nil,
       :letter_grade=>nil,
       :color_code=>nil,
       :submission_status=>nil}],
    "not completed"=>2}],
 :teacher=>nil}
(END)
