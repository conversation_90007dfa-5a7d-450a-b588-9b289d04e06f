# frozen_string_literal: true

module QueryBuilder
  class StudentCourseProgress < BaseQueryBuilder
    def self.from_valid_shard(student, options = {})
      raise ArgumentError, 'student is required' if student.blank?
      raise ArgumentError, 'course_shard_id is required' if options[:course_shard_id].blank?

      course_shard_id = options[:course_shard_id].to_s
      course_progress_data = {}
      request_org = current_organization
      student.against_shards do |shard_student|
        next unless current_organization.canvas_shard_id.to_s == course_shard_id

        course_progress_data = StudentCourseProgress.new({
                                                           canvas_user_id: shard_student.canvas_id,
                                                           canvas_course_id: options[:canvas_course_id],
                                                           filters: options[:filters],
                                                           organization: current_organization,
                                                           request_org: request_org
                                                         }).records
      end
      course_progress_data
    end

    def records
      @user = User.find_by(canvas_id: user_id)
      @course = Course.find_by(canvas_id: course_id)
      course_details = course_details_json
      # Create celebration events for completed modules
      create_module_celebration_events(course_details[:modules])
      course_details.merge!(show_fireworks: pending_module_celebration_event?)
      course_details
    end

    def pending_module_celebration_event?
      CelebrationEvent.exists?(
        canvas_user_id: @user.canvas_id,
        canvas_course_id: @course.canvas_id,
        event_type: 'module_completed',
        shown_at: nil
      )
    end

    def course_details_json
      canvas_course_progress = []
      indexed_canvas_course_progress = {}
      Canvas.with_shard do
        canvas_course_progress = Canvas::Course.course_module_progress(course_id, user_id)
        indexed_canvas_course_progress = canvas_course_progress.index_by { |h| h['context_module_item_id'] }
      end

      course_progress_data = calculate_progress_from_canvas_data(canvas_course_progress)

      course_progress = Course.where(canvas_id: course_id).joins(sql_joins([course_id])).select(sql_selects).first

      {
        id: @course.id,
        canvas_id: @course.canvas_id,
        name: @course.name,
        course_code: @course.course_code,
        course_sis_id: @course.sis_id,
        current_score: course_progress&.current_score,
        current_letter_grade: course_progress&.current_letter_grade,
        requirement_count: course_progress_data[:requirement_count],
        requirement_completed_count: course_progress_data[:requirement_completed_count],
        past_due_requirements_count: course_progress_data[:past_due_requirements_count],
        requirements_with_due_date_count: course_progress_data[:requirements_with_due_date_count],
        course_progress_percent: calculate_course_progress_percent_from_data(course_progress_data),
        # Calculate expected progress from Canvas data
        expected_course_progress: calculate_expected_course_progress_from_data(course_progress_data),
        expected_course_progress_percent: calculate_expected_course_progress_percent_from_data(course_progress_data),
        color_code: fetch_color_code(course_progress&.current_score),
        modules: course_module_items_with_canvas_data(indexed_canvas_course_progress),
        teacher: @course.teachers.first&.name
      }
    end

    # Determines if a submission should be filtered out based on its status and the active filters
    # If a filter is in the filters array, we should filter out items of that type
    def filtered_out?(submission)
      return false if filters.blank?

      (filters.include?('master') && submission.master?) ||
        (filters.include?('not_master') && submission.not_master?) ||
        (filters.include?('not_completed') && submission.not_completed?) ||
        (filters.include?('not_completed_past_due') && submission.not_completed_past_due?)
    end

    # Determines if a module item should be filtered out based on its status and the active filters
    # Works for both assignment and non-assignment items
    def filtered_out_item?(submission, req_status)
      return false if filters.blank?

      # For assignment items, use the existing submission-based filtering
      return filtered_out?(submission) if submission

      # For non-assignment items, filter based on requirement status
      case req_status
      when 'mastered'
        filters.include?('master')
      when 'not mastered'
        filters.include?('not_master')
      when 'past due'
        filters.include?('not_completed_past_due')
      when 'not completed'
        filters.include?('not_completed')
      when 'completed'
        filters.include?('completed')
      else
        false
      end
    end

    def course_sharded_id_string
      organization_shard_id ? course_sharded_id(@course.canvas_id).to_s : @course.canvas_id.to_s
    end

    # Generates the generic Canvas module item path for all content types with sharding support
    def generate_content_path(canvas_data)
      course_sharded_id = course_sharded_id_string
      "courses/#{course_sharded_id}/modules/items/#{canvas_data['context_module_item_id']}"
    end

    private

    # rubocop:disable Metrics/PerceivedComplexity
    def calculate_progress_from_canvas_data(canvas_course_progress)
      course_progress = { requirement_count: 0, requirement_completed_count: 0, past_due_requirements_count: 0, requirements_with_due_date_count: 0 }
      return course_progress if canvas_course_progress.empty?

      requirement_count = canvas_course_progress.length
      requirement_completed_count = canvas_course_progress.count { |item| item['requirement_met'] }

      # Calculate past due count
      past_due_requirements_count = canvas_course_progress.count do |item|
        next false if item['requirement_met']

        due_date = item['todo_date'] || item['lock_at'] || item['assignment_due_date']
        due_date && parse_canvas_date(due_date) && parse_canvas_date(due_date) < Time.current
      rescue ArgumentError
        false
      end

      # Calculate requirements with due date count
      requirements_with_due_date_count = canvas_course_progress.count do |item|
        item['todo_date'].present? || item['lock_at'].present? || item['assignment_due_date'].present?
      end

      course_progress[:requirement_count] = requirement_count
      course_progress[:requirement_completed_count] = requirement_completed_count
      course_progress[:past_due_requirements_count] = past_due_requirements_count
      course_progress[:requirements_with_due_date_count] = requirements_with_due_date_count

      course_progress
    end
    # rubocop:enable Metrics/PerceivedComplexity

    def course_module_items_with_canvas_data(indexed_canvas_course_progress)
      modules = fetch_active_modules
      preloaded_data = preload_module_data(modules)

      build_module_data_with_canvas(modules, preloaded_data, indexed_canvas_course_progress)
    end

    def build_module_data_with_canvas(modules, preloaded_data, indexed_canvas_course_progress)
      modules.map do |context_module|
        module_item_status = indexed_canvas_course_progress
                             .values
                             .find { |v| v['context_module_id'] == context_module.canvas_id }
                             &.dig('module_progress_status')

        items = context_module.context_module_items.filter_map do |item|
          module_item_progress = indexed_canvas_course_progress[item.canvas_id] || {}
          item_data = build_item_data_with_canvas(item, preloaded_data, module_item_progress)
          item_data
        end

        # Calculate module status counts
        status_counts = calculate_module_status_counts(items)

        {
          module_id: context_module.canvas_id,
          name: context_module.name,
          module_progress_status: module_item_status,
          items: items
        }.merge(status_counts)
      end
    end

    # rubocop:disable Metrics/MethodLength
    def build_item_data_with_canvas(item, preloaded_data, module_item_progress)
      submission = preloaded_data[:submissions][item.canvas_assignment_id]
      assignment = preloaded_data[:assignments][item.canvas_assignment_id]

      # Determine requirement status from Canvas data
      requirement_status = determine_requirement_status(module_item_progress, submission)

      return if filtered_out_item?(submission, requirement_status)

      # Parse Canvas dates
      due_date = assignment&.due_at || parse_canvas_date(module_item_progress['todo_date']) || parse_canvas_date(module_item_progress['lock_at'])
      completed_date = submission&.submitted_at
      graded_at = submission&.graded_at

      # Calculate scores and grades
      points_possible = submission&.points_possible
      score = submission&.score
      score_percent = calculate_score_percent(score, points_possible)
      letter_grade = fetch_letter_grade(score)
      color_code = fetch_color_code(score_percent)

      {
        id: item.canvas_id,
        item_type: item.canvas_content_type,
        requirement_status: requirement_status,
        requirement_type: module_item_progress['requirement_type'],
        course_content_path: generate_content_path(module_item_progress),
        organization_shard_id: :organization_shard_id,
        canvas_course_sharded_id: course_sharded_id_string,
        canvas_assignment_id: item.canvas_assignment_id,
        assignment_name: assignment&.title || item.title,
        canvas_user_id: @user.canvas_id.to_s,
        due_date: due_date,
        completed_date: completed_date,
        graded_at: graded_at,
        points_possible: points_possible,
        score: score,
        score_percent: score_percent,
        letter_grade: letter_grade,
        color_code: color_code
      }
    end
    # rubocop:enable Metrics/MethodLength

    def parse_canvas_date(date_value)
      return nil unless date_value.present?
      return date_value if date_value.is_a?(Time)

      Time.zone.parse(date_value.to_s)
    rescue ArgumentError
      nil
    end

    def determine_requirement_status(module_item_progress, submission)
      req_status = requirement_status(module_item_progress)
      return submission.completed_status if submission && req_status == 'completed'

      return submission.requirement_status if submission

      case req_status
      when 'completed'
        'completed'
      when 'incomplete'
        if past_due_from_canvas_data?(module_item_progress)
          'past due'
        else
          'not completed'
        end
      else
        'not completed'
      end
    end

    def past_due_from_canvas_data?(module_item_progress)
      (module_item_progress['todo_date']) && (module_item_progress['todo_date']).to_time < Time.current
    end

    def completed_requirement(row)
      return [] if row['requirements_met'].nil?

      prog_req = YAML.safe_load(row['requirements_met'], permitted_classes: [Symbol])
      prog_req.select { |req| req[:id].to_s == row['context_module_item_id'].to_s }
    end

    def requirement_status(row)
      req_completed = completed_requirement(row)
      req_completed.any? ? 'completed' : 'incomplete'
    end

    def calculate_module_status_counts(items)
      counts = items.group_by { |item| item[:requirement_status] }
                    .transform_values(&:count)
      {
        'items_count' => items.length,
        'mastered' => counts['mastered'] || 0,
        'completed' => counts['completed'] || 0,
        'not mastered' => counts['not mastered'] || 0,
        'not completed' => counts['not completed'] || 0,
        'past due' => counts['past due'] || 0
      }
    end

    def fetch_active_modules
      @course.context_modules
             .includes(:context_module_items)
             .where(workflow_state: 'active')
             .where(context_module_items: { canvas_id: @course.module_requiment_ids, workflow_state: 'active' })
             .order('context_modules.position ASC, context_module_items.position ASC')
    end

    def preload_module_data(modules)
      assignment_ids = modules.flat_map { |mod| mod.context_module_items.map(&:canvas_assignment_id).compact }

      {
        submissions: preload_submissions(assignment_ids),
        assignments: preload_assignments(assignment_ids)
      }
    end

    def preload_submissions(assignment_ids)
      Submission.where(
        canvas_assignment_id: assignment_ids,
        canvas_user_id: @user.canvas_id
      ).index_by(&:canvas_assignment_id)
    end

    def preload_assignments(assignment_ids)
      Assignment.where(canvas_id: assignment_ids).index_by(&:canvas_id)
    end

    def sql_joins(_canvas_course_ids)
      <<~SQL.squish
        LEFT OUTER JOIN #{Enrollment.quoted_table_name} e
          ON e.canvas_user_id = #{user_id}
          AND e.canvas_course_id = courses.canvas_id
          AND e.base_role_type = 'StudentEnrollment' AND (e.workflow_state <> 'deleted')
        LEFT OUTER JOIN #{Score.quoted_table_name} scores ON scores.canvas_enrollment_id = e.canvas_id
      SQL
    end

    def sql_selects
      <<~SQL.squish
        courses.canvas_id AS canvas_course_id,
        e.canvas_id AS canvas_enrollment_id,
        scores.current_score AS current_score,
        scores.current_letter_grade AS current_letter_grade
      SQL
    end

    def calculate_course_progress_percent_from_data(course_progress_data)
      return 0 unless course_progress_data[:requirement_count]&.positive?

      ((course_progress_data[:requirement_completed_count].to_f / course_progress_data[:requirement_count]) * 100).round(0)
    end

    # Expected Course Progress = number of items that should be completed by now (items with past due dates + current items)
    # This represents what percentage of the course should be completed based on due dates
    def calculate_expected_course_progress_from_data(course_progress_data)
      return 0 unless course_progress_data[:requirements_with_due_date_count]&.positive?

      # Expected progress should be 100% if all items with due dates are past due
      # This means the student should have completed all items with due dates by now
      past_due_items = course_progress_data[:past_due_requirements_count]

      # If there are past due items, expected progress should be 100%
      # (all items with due dates should be completed)
      return 100 if past_due_items.positive?

      # If no items are past due yet, expected progress is based on current timeline
      # For now, return 100% as the baseline expectation
      100
    end

    # Percentage of Expected Course Progress = percentage of items completed vs Expected Course Progress
    def calculate_expected_course_progress_percent_from_data(course_progress_data)
      expected_progress = calculate_expected_course_progress_from_data(course_progress_data)
      return 0 if expected_progress.zero?

      actual_progress = calculate_course_progress_percent_from_data(course_progress_data)
      (actual_progress.to_f / expected_progress * 100).round(0)
    end

    def calculate_score_percent(score, points_possible)
      return 0.0 if points_possible.to_f <= 0

      ((score.to_f / points_possible) * 100).round(2)
    end

    def fetch_letter_grade(score)
      return nil if score.nil? || grading_scheme.nil?

      grading_scheme&.score_to_grade(score)&.dig('name')
    end

    def fetch_color_code(score)
      return nil if score.nil? || grading_scheme_colors.nil?

      grading_scheme_color = grading_scheme_colors&.by_score(score)
      grading_scheme_color&.color_code || grading_scheme_color&.default_color_code
    end

    def grading_scheme
      @grading_scheme ||= @course&.grading_scheme
    end

    def grading_scheme_colors
      @grading_scheme_colors ||= grading_scheme&.grading_scheme_colors
    end

    def create_module_celebration_events(modules)
      modules.each do |mod|
        celebration_event = CelebrationEvent.find_by(
          canvas_user_id: @user.canvas_id,
          canvas_course_id: @course.canvas_id,
          event_type: 'module_completed',
          canvas_module_id: mod[:module_id]
        )

        if mod[:module_progress_status] != 'completed' && celebration_event.present?
          # If the module is no longer completed, remove any existing celebration event
          celebration_event.destroy
        end

        next unless mod[:module_progress_status] == 'completed'

        CelebrationEvent.find_or_create_by!(
          canvas_user_id: @user.canvas_id,
          canvas_course_id: @course.canvas_id,
          event_type: 'module_completed',
          canvas_module_id: mod[:module_id]
        )
      end
    rescue StandardError => e
      Rails.logger.error "Error creating module celebration events: #{e.message}"
      Rails.logger.error e.backtrace.join("\n")
      raise e
    end
  end
end
