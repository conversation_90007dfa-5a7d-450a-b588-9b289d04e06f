# frozen_string_literal: true

module QueryBuilder
  class StudentCourses < BaseQueryBuilder
    def self.across_shard(student)
      all_courses = []
      student.against_shards do |shard_student|
        courses = StudentCourses.new({ canvas_user_id: shard_student.canvas_id, organization: current_organization }).records
        all_courses += courses
      end
      all_courses
    end

    def records
      uniq_enrollment_ids = student_uniq_course_enrollment_ids.map(&:canvas_id)
      canvas_course_ids = student_uniq_course_enrollment_ids.map(&:canvas_course_id)

      courses = []
      Canvas.with_shard do
        course_progresses = {}
        result = Canvas::Course.progress_count(canvas_course_ids, user_id)
        result.each do |row|
          course_progresses[row['id']] = {
            requirement_count: row['req_count'],
            requirement_completed_count: row['req_completed_count']
          }
        end

        courses = Enrollment.where(canvas_id: uniq_enrollment_ids)
                            .left_outer_joins(:course, :score)
                            .where("courses.workflow_state IN ('active', 'available')")
                            .select(sql_selects)
                            .order('courses.name ASC')
        courses.each do |course|
          course.singleton_class.class_eval do
            attr_accessor :requirement_count, :requirement_completed_count
          end
          progress = course_progresses[course.canvas_course_id] || {}
          course.requirement_count = progress[:requirement_count].to_i
          course.requirement_completed_count = progress[:requirement_completed_count].to_i
        end
      end
      courses
    end

    def sql_selects
      <<~SQL.squish
        courses.id AS id,
        courses.canvas_id AS canvas_course_id,
        courses.name AS name,
        courses.sis_id AS course_sis_id,
        courses.course_code AS course_code,
        courses.workflow_state AS course_state,
        courses.image_url AS course_image_url,
        courses.grade_level AS grade_level,
        enrollments.canvas_id AS canvas_enrollment_id,
        enrollments.canvas_user_id AS canvas_user_id,
        enrollments.workflow_state AS enrollment_state,
        scores.current_score AS current_score,
        scores.current_letter_grade AS current_letter_grade,
        '#{organization_id}' AS organization_id,
        '#{organization_name}' AS organization_name,
        #{organization_shard_id} AS organization_shard_id
      SQL
    end

    def student_uniq_course_enrollment_ids
      Enrollment.students.selectable
                .group('enrollments.canvas_course_id')
                .select('min(canvas_id) as canvas_id, canvas_course_id')
                .where(canvas_user_id: user_id)
    end
  end
end
