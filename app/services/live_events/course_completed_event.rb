# frozen_string_literal: true

module LiveEvents
  class CourseCompletedEvent < CanvasSync::LiveEvents::BaseHandler
    def process
      Rails.logger.info "Processing CourseCompletedEvent for course_id: #{payload[:course][:id]} and user_id: #{payload[:user][:id]}"
      course_id = local_canvas_id(payload[:course][:id])
      course = Course.find_by(canvas_id: course_id)
      return unless course

      # Do not convert the user ID to a local ID, as we get sharded user id in payload only in case if user belongs to another shard.
      user = User.find_by(canvas_id: payload[:user][:id])
      return unless user

      student_id = user.canvas_id if user&.k5_student? && user.canvas_id.present?
      return unless student_id

      StudentCourseProgressJob.perform_later(course_id: course_id, student_id: student_id)
    end

    private

    # Live events will use a canvas global ID (cross shard) for any ID's provided. This method will return the local ID.
    def local_canvas_id(id)
      id.to_i % 10_000_000_000_000
    end
  end
end
